plugins:
  - rubocop-factory_bot
  - rubocop-performance
  - rubocop-performance
  - rubocop-rspec

require:
  - rubocop-discourse
  - rubocop-rails

inherit_gem:
  pundit: config/rubocop-rspec.yml

AllCops:
  NewCops: enable
  TargetRubyVersion: 3.2.2
  TargetRailsVersion: 7.0.4
  Include:
    - '**/*.rb'
  Exclude:
    - bin/**/*
    - db/migrate/*_init_schema.rb
    - db/schema.rb
    - vendor/**/*
    - volumes/bundle/**/*

# Layout/IndentationConsistency:
  # EnforcedStyle: indented_internal_methods

Layout/FirstArrayElementIndentation:
  EnforcedStyle: consistent

Layout/MultilineHashBraceLayout:
  Enabled: false

Layout/MultilineMethodCallIndentation:
  EnforcedStyle: indented

Layout/EmptyLinesAroundAttributeAccessor:
  Enabled: true

Layout/EndAlignment:
  EnforcedStyleAlignWith: variable

Layout/LineLength:
  Max: 120

Layout/SpaceAroundMethodCallOperator:
  Enabled: true

Layout/SpaceInsideArrayLiteralBrackets:
  EnforcedStyle: space

Lint/AmbiguousBlockAssociation:
  Exclude:
    - "spec/**/*"

Lint/DeprecatedOpenSSLConstant:
  Enabled: true

Lint/RaiseException:
  Enabled: true

Lint/MixedRegexpCaptureTypes:
  Enabled: true

Lint/StructNewOverride:
  Enabled: true

Lint/UnusedMethodArgument:
  AllowUnusedKeywordArguments: true

Metrics/AbcSize:
  Max: 20
  Exclude:
    - 'db/migrate/*.rb'
    - 'app/mailers/*.rb'

Metrics/BlockLength:
  Max: 30
  Exclude:
    - 'spec/**/*.rb'
    - 'lib/tasks/*.rake'
    - 'lib/intuo/data_generator.rb'
    - 'config/deploy.rb'
    - 'app/models/concerns/**/*.rb'

Metrics/ClassLength:
  Max: 350

Metrics/MethodLength:
  Max: 15
  Exclude:
    - 'db/migrate/*.rb'

Metrics/ModuleLength:
  Max: 32768

Naming/PredicateName:
  NamePrefix:
    - has_
    - have_
    - is_
  ForbiddenPrefixes:
    - is_
  Exclude:
    - 'app/serializers/**/*.rb'

Naming/MethodParameterName:
  Enabled: false

Rails:
  Enabled: true

Rails/HasAndBelongsToMany:
  Enabled: false

Rails/DynamicFindBy:
  Exclude:
    - 'app/services/emoji_processor.rb'

Rails/SkipsModelValidations:
  Exclude:
    - 'db/migrate/**/*.rb'
    - 'app/services/follow_objective_service.rb'
    - 'app/services/api/v1/objective_service.rb'
  ForbiddenMethods:
    - decrement!
    - decrement_counter
    - increment!
    - increment_counter
    - toggle!
    - update_attribute
    - update_column
    - update_columns
    - update_counters

Rails/InverseOf:
  Enabled: false

Rails/HttpStatus:
  Enabled: true
  EnforcedStyle: symbolic

Rails/Output:
  Exclude:
    - 'lib/demo/demo_data_import.rb'

RSpec/MultipleExpectations:
  Max: 10
  Exclude:
    - 'spec/features/**/*.rb'

# RSpec/NestedGroups:
#   Max: 4

# RSpec/MultipleMemoizedHelpers:
#   Max: 10

# RSpec/ExampleLength:
#   Exclude:
#     - 'spec/features/**/*.rb'

Security/MarshalLoad:
  Enabled: true
  Exclude:
    - 'spec/requests/saml_controller_spec.rb'

Style/BlockDelimiters:
  EnforcedStyle: braces_for_chaining

Style/Documentation:
  Enabled: false

Style/ExponentialNotation:
  Enabled: true

Style/FormatStringToken:
  EnforcedStyle: template

Style/FrozenStringLiteralComment:
  Enabled: false

Style/HashEachMethods:
  Enabled: false

Style/HashSyntax:
  EnforcedStyle: ruby19_no_mixed_keys

Style/HashTransformKeys:
  Enabled: false

Style/HashTransformValues:
    Enabled: false

Style/PercentLiteralDelimiters:
  PreferredDelimiters:
    default: '[]'
    '%r': '||'

Style/RegexpLiteral:
  EnforcedStyle: mixed
  AllowInnerSlashes: false

Style/RedundantRegexpCharacterClass:
  Enabled: true

Style/RedundantRegexpEscape:
  Enabled: true

Style/RedundantReturn:
  AllowMultipleReturnValues: true

Style/SlicingWithRange:
  Enabled: true

# RSpec/InstanceVariable:
#   Enabled: true
#   Exclude:
#     - 'spec/features/perform/**/*.rb' # Our feature tests really depend on calling @client and @admin

Style/TrailingCommaInArrayLiteral:
  Enabled: false

RSpec/ContextWording:
  Enabled: false
