Rails.application.routes.draw do
  resources :broodiness_periods
  resources :chickens
  resources :cities
  resources :continents
  resources :countries
  resources :currencies
  resources :document_types
  resources :documents
  resources :eggs
  resources :food_events
  resources :food_types
  resources :gift_events
  resources :images
  resources :persona_types
  resources :personas
  resources :product_categories
  resources :products
  resources :purchase_events
  resources :refuel_events
  resources :refuel_stations
  resources :relationships
  resources :sell_events
  resources :unit_categories
  resources :units
  resources :vet_clinics
  resources :vet_visits
  resources :weight_records
  devise_for :users
  get 'static_page/home'
  get 'static_page/heading_test'
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Defines the root path route ("/")
  root 'static_page#home'

  get 'chickens/:chicken_id/eggs', to: 'eggs#index', as: 'chicken_eggs'
  get 'chickens/:chicken_id/broodiness_periods', to: 'broodiness_periods#index', as: 'chicken_broodiness_periods'

  # Allow ActiveStorage and other Rails paths to function while catching other issues globally
  match '*path', to: 'static_page#home', via: :all, constraints: ->(req) { !req.path.starts_with?('/rails/') }
end
