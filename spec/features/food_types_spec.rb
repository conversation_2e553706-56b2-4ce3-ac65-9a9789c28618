require 'rails_helper'

RSpec.describe 'FoodTypes', type: :system do
  let(:user) { create(:user, role: :contributor) }

  before { sign_in user }

  it 'allows a user to create a new food type' do
    visit new_food_type_path
    fill_in 'Name', with: 'Grain'
    click_button 'Create'
    expect(page).to have_content('Food type was successfully created')
    expect(page).to have_content('Grain')
  end

  it 'shows a list of food types' do
    create(:food_type, name: '<PERSON>rn')
    visit food_types_path
    expect(page).to have_content('Corn')
  end

  it 'allows a user to edit a food type' do
    food_type = create(:food_type, name: 'Seed')
    visit edit_food_type_path(food_type)
    fill_in 'Name', with: 'Sunflower Seed'
    click_button 'Update'
    expect(page).to have_content('Sunflower Seed')
  end

  it 'allows a user to delete a food type', :js do
    food_type = create(:food_type, name: '<PERSON>ellet')
    visit food_type_path(food_type)
    crud_confirm_and_delete
    expect(page).to have_no_content('<PERSON>ellet')
  end
end
