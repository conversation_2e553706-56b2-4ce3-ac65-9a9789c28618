require 'capybara/rspec'
require 'selenium-webdriver'

# Disable automatic driver updates
ENV['WD_DISABLE_DOWNLOAD'] = 'true'

# Configure Capybara
Capybara.register_driver :chrome do |app|
  options = Selenium::WebDriver::Chrome::Options.new

  # Set Chromium binary path if in Docker
  if File.exist?('/.dockerenv')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.binary = '/usr/bin/chromium'
  end

  # Use system-installed chromedriver in Docker
  service = if File.exist?('/.dockerenv')
    Selenium::WebDriver::Service.chrome(path: '/usr/bin/chromedriver')
  else
    Selenium::WebDriver::Service.chrome
  end

  Capybara::Selenium::Driver.new(
    app,
    browser: :chrome,
    options:,
    service:
  )
end

Capybara.register_driver :headless_chrome do |app|
  options = Selenium::WebDriver::Chrome::Options.new
  options.add_argument('--headless')
  options.add_argument('--disable-gpu')
  options.add_argument('--no-sandbox')
  options.add_argument('--disable-dev-shm-usage')

  # Set Chromium binary path if in Docker
  if File.exist?('/.dockerenv')
    options.add_argument('--disable-setuid-sandbox')
    options.binary = '/usr/bin/chromium'
  end

  # Use system-installed chromedriver in Docker
  service = if File.exist?('/.dockerenv')
    Selenium::WebDriver::Service.chrome(path: '/usr/bin/chromedriver')
  else
    Selenium::WebDriver::Service.chrome
  end

  Capybara::Selenium::Driver.new(
    app,
    browser: :chrome,
    options:,
    service:
  )
end

# Use headless Chrome in CI environment, regular Chrome locally
is_docker = File.exist?('/.dockerenv')
Capybara.javascript_driver = ENV['CI'] || is_docker ? :headless_chrome : :chrome

# Configure Capybara
Capybara.configure do |config|
  config.default_max_wait_time = 5
  # Use rack_test by default for speed
  config.default_driver = :rack_test
  config.server = :puma, { Silent: true }
end
