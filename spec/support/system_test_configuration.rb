RSpec.configure do |config|
  # Check if we're running in CI or Docker
  is_docker = File.exist?('/.dockerenv')
  is_ci = ENV['CI'] == 'true'

  # Use rack_test by default for speed (non-JS tests)
  config.before(:each, type: :system) do
    driven_by :rack_test
  end

  # Configure JS tests based on environment
  if is_ci
    # Use headless Chrome in CI environment
    config.before(:each, :js, type: :system) do
      driven_by :headless_chrome
    end
  elsif is_docker
    # Use headless Chrome in Docker for stability
    config.before(:each, :js, type: :system) do
      driven_by :headless_chrome
    end
  else
    # Use regular Chrome locally for easier debugging
    config.before(:each, :js, type: :system) do
      driven_by :chrome
    end
  end
end
