require 'rails_helper'

RSpec.describe 'broodiness_periods/index', type: :view do
  before do
    assign(:broodiness_periods, [
      BroodinessPeriod.create!(
        chicken: nil
      ),
      BroodinessPeriod.create!(
        chicken: nil
      )
    ])
  end

  it 'renders a list of broodiness_periods' do
    render
    cell_selector = 'div>p'
    assert_select cell_selector, text: Regexp.new(nil.to_s), count: 2
  end
end
