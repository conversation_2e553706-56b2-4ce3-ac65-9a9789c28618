require 'rails_helper'

RSpec.describe 'broodiness_periods/index', type: :view do
  before do
    BroodinessPeriod.create!(chicken: create(:chicken))
    BroodinessPeriod.create!(chicken: create(:chicken))

    broodiness_periods = BroodinessPeriod.all
    assign(:broodiness_periods, broodiness_periods)
  end

  it 'renders a list of broodiness_periods' do
    render
    cell_selector = 'div>p'
    assert_select cell_selector, text: Regexp.new(nil.to_s), count: 2
  end
end
