require 'rails_helper'

RSpec.describe 'broodiness_periods/edit', type: :view do
  let(:broodiness_period) do
    BroodinessPeriod.create!(
      chicken: nil
    )
  end

  before do
    assign(:broodiness_period, broodiness_period)
  end

  it 'renders the edit broodiness_period form' do
    render

    assert_select 'form[action=?][method=?]', broodiness_period_path(broodiness_period), 'post' do
      assert_select 'input[name=?]', 'broodiness_period[chicken_id]'
    end
  end
end
