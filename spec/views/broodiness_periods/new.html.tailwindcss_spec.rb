require 'rails_helper'

RSpec.describe 'broodiness_periods/new', type: :view do
  before do
    assign(:broodiness_period, BroodinessPeriod.new(
                                 chicken: nil
                               ))
  end

  it 'renders new broodiness_period form' do
    render

    assert_select 'form[action=?][method=?]', broodiness_periods_path, 'post' do
      assert_select 'input[name=?]', 'broodiness_period[chicken_id]'
    end
  end
end
