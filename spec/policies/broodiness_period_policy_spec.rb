require 'rails_helper'

RSpec.describe BroodinessPeriodPolicy, type: :policy do
  subject { described_class }

  let(:broodiness_period) { create(:broodiness_period) }

  permissions '.scope' do
    let(:scope) { BroodinessPeriod.all }

    context 'when user is nil' do
      let(:user) { nil }

      it 'returns all broodiness periods' do
        expect(Pundit.policy_scope(user, scope)).to eq(scope)
      end
    end

    context 'when user is present' do
      let(:user) { create(:user) }

      it 'returns all broodiness periods' do
        expect(Pundit.policy_scope(user, scope)).to eq(scope)
      end
    end
  end

  permissions :show? do
    context 'when user is nil' do
      let(:user) { nil }

      it 'denies access' do
        expect(subject).not_to permit(user, broodiness_period)
      end
    end

    context 'when user is regular' do
      let(:user) { create(:user, role: :regular) }

      it 'denies access' do
        expect(subject).not_to permit(user, broodiness_period)
      end
    end

    context 'when user is contributor' do
      let(:user) { create(:user, role: :contributor) }

      it 'permits access' do
        expect(subject).to permit(user, broodiness_period)
      end
    end

    context 'when user is admin' do
      let(:user) { create(:user, role: :admin) }

      it 'permits access' do
        expect(subject).to permit(user, broodiness_period)
      end
    end
  end

  permissions :create? do
    context 'when user is nil' do
      let(:user) { nil }

      it 'denies access' do
        expect(subject).not_to permit(user, BroodinessPeriod)
      end
    end

    context 'when user is regular' do
      let(:user) { create(:user, role: :regular) }

      it 'denies access' do
        expect(subject).not_to permit(user, BroodinessPeriod)
      end
    end

    context 'when user is contributor' do
      let(:user) { create(:user, role: :contributor) }

      it 'permits access' do
        expect(subject).to permit(user, BroodinessPeriod)
      end
    end

    context 'when user is admin' do
      let(:user) { create(:user, role: :admin) }

      it 'permits access' do
        expect(subject).to permit(user, BroodinessPeriod)
      end
    end
  end

  permissions :update? do
    context 'when user is nil' do
      let(:user) { nil }

      it 'denies access' do
        expect(subject).not_to permit(user, broodiness_period)
      end
    end

    context 'when user is regular' do
      let(:user) { create(:user, role: :regular) }

      it 'denies access' do
        expect(subject).not_to permit(user, broodiness_period)
      end
    end

    context 'when user is contributor' do
      let(:user) { create(:user, role: :contributor) }

      it 'permits access' do
        expect(subject).to permit(user, broodiness_period)
      end
    end

    context 'when user is admin' do
      let(:user) { create(:user, role: :admin) }

      it 'permits access' do
        expect(subject).to permit(user, broodiness_period)
      end
    end
  end

  permissions :destroy? do
    context 'when user is nil' do
      let(:user) { nil }

      it 'denies access' do
        expect(subject).not_to permit(user, broodiness_period)
      end
    end

    context 'when user is regular' do
      let(:user) { create(:user, role: :regular) }

      it 'denies access' do
        expect(subject).not_to permit(user, broodiness_period)
      end
    end

    context 'when user is contributor' do
      let(:user) { create(:user, role: :contributor) }

      it 'permits access' do
        expect(subject).to permit(user, broodiness_period)
      end
    end

    context 'when user is admin' do
      let(:user) { create(:user, role: :admin) }

      it 'permits access' do
        expect(subject).to permit(user, broodiness_period)
      end
    end
  end

  permissions :index? do
    context 'when user is nil' do
      let(:user) { nil }

      it 'denies access' do
        expect(subject).not_to permit(user, BroodinessPeriod)
      end
    end

    context 'when user is regular' do
      let(:user) { create(:user, role: :regular) }

      it 'denies access' do
        expect(subject).not_to permit(user, BroodinessPeriod)
      end
    end

    context 'when user is contributor' do
      let(:user) { create(:user, role: :contributor) }

      it 'permits access' do
        expect(subject).to permit(user, BroodinessPeriod)
      end
    end

    context 'when user is admin' do
      let(:user) { create(:user, role: :admin) }

      it 'permits access' do
        expect(subject).to permit(user, BroodinessPeriod)
      end
    end
  end

  permissions :new? do
    context 'when user is nil' do
      let(:user) { nil }

      it 'denies access' do
        expect(subject).not_to permit(user, BroodinessPeriod)
      end
    end

    context 'when user is regular' do
      let(:user) { create(:user, role: :regular) }

      it 'denies access' do
        expect(subject).not_to permit(user, BroodinessPeriod)
      end
    end

    context 'when user is contributor' do
      let(:user) { create(:user, role: :contributor) }

      it 'permits access' do
        expect(subject).to permit(user, BroodinessPeriod)
      end
    end

    context 'when user is admin' do
      let(:user) { create(:user, role: :admin) }

      it 'permits access' do
        expect(subject).to permit(user, BroodinessPeriod)
      end
    end
  end

  permissions :edit? do
    context 'when user is nil' do
      let(:user) { nil }

      it 'denies access' do
        expect(subject).not_to permit(user, broodiness_period)
      end
    end

    context 'when user is regular' do
      let(:user) { create(:user, role: :regular) }

      it 'denies access' do
        expect(subject).not_to permit(user, broodiness_period)
      end
    end

    context 'when user is contributor' do
      let(:user) { create(:user, role: :contributor) }

      it 'permits access' do
        expect(subject).to permit(user, broodiness_period)
      end
    end

    context 'when user is admin' do
      let(:user) { create(:user, role: :admin) }

      it 'permits access' do
        expect(subject).to permit(user, broodiness_period)
      end
    end
  end
end
