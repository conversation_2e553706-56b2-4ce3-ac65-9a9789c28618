require 'rails_helper'

# This spec was generated by rspec-rails when you ran the scaffold generator.
# It demonstrates how one might use RSpec to test the controller code that
# was generated by Rails when you ran the scaffold generator.
#
# It assumes that the implementation code is generated by the rails scaffold
# generator. If you are using any extension libraries to generate different
# controller code, this generated spec may or may not pass.
#
# It only uses APIs available in rails and/or rspec-rails. There are a number
# of tools you can use to make these specs even more expressive, but we're
# sticking to rails and rspec-rails APIs to keep things simple and stable.

RSpec.describe '/broodiness_periods', type: :request do
  # This should return the minimal set of attributes required to create a valid
  # BroodinessPeriod. As you add validations to BroodinessPeriod, be sure to
  # adjust the attributes here as well.
  let(:valid_attributes) do
    skip('Add a hash of attributes valid for your model')
  end

  let(:invalid_attributes) do
    skip('Add a hash of attributes invalid for your model')
  end

  describe 'GET /index' do
    it 'renders a successful response' do
      BroodinessPeriod.create! valid_attributes
      get broodiness_periods_url
      expect(response).to be_successful
    end
  end

  describe 'GET /show' do
    it 'renders a successful response' do
      broodiness_period = BroodinessPeriod.create! valid_attributes
      get broodiness_period_url(broodiness_period)
      expect(response).to be_successful
    end
  end

  describe 'GET /new' do
    it 'renders a successful response' do
      get new_broodiness_period_url
      expect(response).to be_successful
    end
  end

  describe 'GET /edit' do
    it 'renders a successful response' do
      broodiness_period = BroodinessPeriod.create! valid_attributes
      get edit_broodiness_period_url(broodiness_period)
      expect(response).to be_successful
    end
  end

  describe 'POST /create' do
    context 'with valid parameters' do
      it 'creates a new BroodinessPeriod' do
        expect {
          post broodiness_periods_url, params: { broodiness_period: valid_attributes }
        }.to change(BroodinessPeriod, :count).by(1)
      end

      it 'redirects to the created broodiness_period' do
        post broodiness_periods_url, params: { broodiness_period: valid_attributes }
        expect(response).to redirect_to(broodiness_period_url(BroodinessPeriod.last))
      end
    end

    context 'with invalid parameters' do
      it 'does not create a new BroodinessPeriod' do
        expect {
          post broodiness_periods_url, params: { broodiness_period: invalid_attributes }
        }.not_to change(BroodinessPeriod, :count)
      end

      it "renders a response with 422 status (i.e. to display the 'new' template)" do
        post broodiness_periods_url, params: { broodiness_period: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe 'PATCH /update' do
    context 'with valid parameters' do
      let(:new_attributes) do
        skip('Add a hash of attributes valid for your model')
      end

      it 'updates the requested broodiness_period' do
        broodiness_period = BroodinessPeriod.create! valid_attributes
        patch broodiness_period_url(broodiness_period), params: { broodiness_period: new_attributes }
        broodiness_period.reload
        skip('Add assertions for updated state')
      end

      it 'redirects to the broodiness_period' do
        broodiness_period = BroodinessPeriod.create! valid_attributes
        patch broodiness_period_url(broodiness_period), params: { broodiness_period: new_attributes }
        broodiness_period.reload
        expect(response).to redirect_to(broodiness_period_url(broodiness_period))
      end
    end

    context 'with invalid parameters' do
      it "renders a response with 422 status (i.e. to display the 'edit' template)" do
        broodiness_period = BroodinessPeriod.create! valid_attributes
        patch broodiness_period_url(broodiness_period), params: { broodiness_period: invalid_attributes }
        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe 'DELETE /destroy' do
    it 'destroys the requested broodiness_period' do
      broodiness_period = BroodinessPeriod.create! valid_attributes
      expect {
        delete broodiness_period_url(broodiness_period)
      }.to change(BroodinessPeriod, :count).by(-1)
    end

    it 'redirects to the broodiness_periods list' do
      broodiness_period = BroodinessPeriod.create! valid_attributes
      delete broodiness_period_url(broodiness_period)
      expect(response).to redirect_to(broodiness_periods_url)
    end
  end
end
