require 'rails_helper'

RSpec.describe Egg, type: :model do
  it { is_expected.to validate_presence_of(:laid_at) }

  describe 'validations' do
    it 'validates that the egg is laid by some available chicken' do
      build(:chicken)
      egg = build(:egg, chickens: [ build(:chicken) ])

      expect(egg).to be_valid
    end

    it 'adds an error if no chickens are available to lay the egg' do
      chicken = build(:chicken, birth_day: DateTime.tomorrow)
      egg = build(:egg, chickens: [ chicken ])

      expect(egg).not_to be_valid
      expect(egg.errors[:chickens]).to include('is invalid')
    end

    it 'validates unknown_fate must be false if associated with an event' do
      egg = build(:egg, food_event: create(:food_event), unknown_fate: true)

      expect(egg).not_to be_valid
      expect(egg.errors[:unknown_fate]).to include('cannot be true if associated with a food event or gift event')
    end
  end

  describe 'scopes' do
    it 'returns available eggs' do
      available_egg = create(:egg, food_event_id: nil, gift_event_id: nil, unknown_fate: false)
      expect(described_class.available).to include(available_egg)

      unavailable_eggs = [
        create(:egg, food_event: create(:food_event)),
        create(:egg, gift_event: create(:gift_event)),
        create(:egg, unknown_fate: true),
      ]
      unavailable_eggs.each { |egg| expect(described_class.available).not_to include(egg) }
    end

    it 'returns cooked eggs' do
      cooked_egg = create(:egg, food_event: create(:food_event))
      raw_egg = create(:egg, food_event_id: nil)

      expect(described_class.cooked).to include(cooked_egg)
      expect(described_class.cooked).not_to include(raw_egg)
    end
  end

  describe '#avatar' do
    it 'returns the first attached image if present' do
      egg = create(:egg)
      image = fixture_file_upload(Rails.root.join('spec/fixtures/files/sample.jpg'), 'image/jpeg')
      egg.images.attach(image)

      expect(egg.avatar).to eq(egg.images.first)
    end

    it 'returns the default avatar path if no images are attached' do
      egg = build(:egg)

      expect(egg.avatar).to eq(ActionController::Base.helpers.asset_path('Realistic-egg.svg'))
    end
  end

  describe '#siblings' do
    it 'returns eggs laid on the same day excluding itself' do
      egg1 = create(:egg, laid_at: Time.zone.now)
      egg2 = create(:egg, laid_at: Time.zone.now)
      egg3 = create(:egg, laid_at: 2.days.ago)

      expect(egg1.siblings).to include(egg2)
      expect(egg1.siblings).not_to include(egg1)
      expect(egg1.siblings).not_to include(egg3)
    end
  end

  describe '#data_missing?' do
    it 'returns true if image is missing' do
      egg = create(:egg)
      egg.chickens << create(:chicken)

      expect(egg.data_missing?).to be true
    end

    it 'returns true if we don’t know who has laid this egg' do
      egg = create(:egg)

      image = fixture_file_upload(Rails.root.join('spec/fixtures/files/sample.jpg'), 'image/jpeg')
      egg.images.attach(image)

      expect(egg.data_missing?).to be true
    end

    it 'returns true if any required attribute is missing' do
      egg = create(:egg)

      image = fixture_file_upload(Rails.root.join('spec/fixtures/files/sample.jpg'), 'image/jpeg')
      egg.images.attach(image)

      egg.chickens << create(:chicken)

      required_attributes = %i[height width weight]
      required_attributes.each do |attr|
        egg.send(:"#{attr}=", nil)
        expect(egg.data_missing?).to be true
        egg.send(:"#{attr}=", 1.0)
      end
    end

    it 'returns false if all required data is present' do
      chicken = create(:chicken)
      egg = build(:egg, weight: 50, width: 5, height: 5, chickens: [ chicken ])
      image = fixture_file_upload(Rails.root.join('spec/fixtures/files/sample.jpg'), 'image/jpeg')
      egg.images.attach(image)

      expect(egg.data_missing?).to be false
    end
  end
end
