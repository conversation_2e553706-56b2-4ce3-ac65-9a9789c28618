require 'rails_helper'

RSpec.describe User, type: :model do
  describe 'enums' do
    it 'defines roles with correct values' do
      expect(described_class.roles).to eq({ 'regular' => 0, 'contributor' => 1, 'admin' => 2 })
    end
  end

  describe '#contributor_or_admin?' do
    let(:user) { described_class.new(role:) }

    context 'when the user is a contributor' do
      let(:role) { 'contributor' }

      it 'returns true' do
        expect(user.contributor_or_admin?).to be true
      end
    end

    context 'when the user is an admin' do
      let(:role) { 'admin' }

      it 'returns true' do
        expect(user.contributor_or_admin?).to be true
      end
    end

    context 'when the user is regular' do
      let(:role) { 'regular' }

      it 'returns false' do
        expect(user.contributor_or_admin?).to be false
      end
    end
  end
end
