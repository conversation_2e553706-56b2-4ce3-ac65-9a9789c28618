require 'rails_helper'

RSpec.describe Gender, type: :model do
  describe 'validations' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_inclusion_of(:name).in_array(%w[male female]) }
    it { is_expected.to validate_uniqueness_of(:name) }
  end

  describe '.female' do
    let!(:female_gender) { described_class.find_or_create_by!(name: 'female') }

    it 'returns the gender with name "female"' do
      expect(described_class.female).to eq(female_gender)
    end
  end

  describe '.male' do
    let!(:male_gender) { described_class.find_or_create_by!(name: 'male') }

    it 'returns the gender with name "male"' do
      expect(described_class.male).to eq(male_gender)
    end
  end
end
