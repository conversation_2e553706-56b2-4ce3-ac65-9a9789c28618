require 'rails_helper'

RSpec.describe RefuelEvent, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:refuel_station) }
    it { is_expected.to belong_to(:currency) }
    it { is_expected.to belong_to(:unit) }
  end

  describe 'validations' do
    it { is_expected.to validate_numericality_of(:amount).is_greater_than_or_equal_to(0) }
    it { is_expected.to validate_numericality_of(:paid_price).is_greater_than_or_equal_to(0) }
    it { is_expected.to validate_numericality_of(:price_per_unit).is_greater_than_or_equal_to(0) }
  end
end
