require 'rails_helper'

RSpec.describe VetVisit, type: :model do
  describe 'validations' do
    subject do
      described_class.new(
        severity: :moderate,
        date: Time.zone.today,
        chicken:,
        vet_clinic:,
        vet:,
        previous_visit:,
        at_home: false
      )
    end

    let(:chicken) { create(:chicken) }
    let(:vet_clinic) { create(:vet_clinic) }
    let(:vet) { create(:persona) }
    let(:previous_visit) { create(:vet_visit, date: 1.day.ago, chicken:, vet_clinic:, vet:) }

    it 'is valid with valid attributes' do
      expect(subject).to be_valid
    end

    it { is_expected.to validate_presence_of(:date) }
    it { is_expected.to validate_presence_of(:severity) }

    context 'either_at_home_or_in_clicnic validation' do
      it 'is valid when at_home is true and vet_clinic is blank' do
        subject.at_home = true
        subject.vet_clinic = nil
        expect(subject).to be_valid
      end

      it 'is valid when at_home is false and vet_clinic is present' do
        subject.at_home = false
        subject.vet_clinic = vet_clinic
        expect(subject).to be_valid
      end

      it 'is invalid when at_home is true and vet_clinic is present' do
        subject.at_home = true
        subject.vet_clinic = vet_clinic
        expect(subject).not_to be_valid
        expect(subject.errors[:vet_clinic]).to include('is invalid')
      end

      it 'is invalid when at_home is false and vet_clinic is blank' do
        subject.at_home = false
        subject.vet_clinic = nil
        expect(subject).not_to be_valid
        expect(subject.errors[:vet_clinic]).to include('is invalid')
      end
    end

    context 'previous_visit_cannot_be_after_current_visit validation' do
      it 'is valid when previous_visit date is before current visit date' do
        subject.previous_visit = previous_visit
        subject.date = Time.zone.today
        expect(subject).to be_valid
      end

      it 'is invalid when previous_visit date is after or equal to current visit date' do
        subject.previous_visit = previous_visit
        subject.date = previous_visit.date - 1.hour
        expect(subject).not_to be_valid
        expect(subject.errors[:previous_visit]).to include('is invalid')
      end

      it 'is valid when there is no previous_visit' do
        subject.previous_visit = nil
        expect(subject).to be_valid
      end
    end
  end

  describe 'associations' do
    it { is_expected.to belong_to(:chicken) }
    it { is_expected.to belong_to(:previous_visit).class_name('VetVisit').optional }
    it { is_expected.to belong_to(:vet).class_name('Persona') }
    it { is_expected.to have_many(:vet_visits).dependent(:destroy) }
  end
end
