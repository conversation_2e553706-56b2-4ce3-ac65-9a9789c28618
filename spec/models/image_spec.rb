require 'rails_helper'

RSpec.describe Image, type: :model do
  subject { create(:image) }

  describe 'associations' do
    it { is_expected.to have_one_attached(:file) }
    it { is_expected.to have_many(:image_associations).dependent(:destroy) }
  end

  describe 'validations' do
    it { is_expected.to validate_length_of(:label).is_at_least(1).is_at_most(256).allow_blank }
    it { is_expected.to validate_length_of(:description).is_at_least(1).is_at_most(10_000).allow_blank }
  end

  describe '#imageables' do
    let(:chicken) { create(:chicken) }
    let(:vet_clinic) { create(:vet_clinic) }

    before do
      subject.image_associations.create!(imageable: chicken)
      subject.image_associations.create!(imageable: vet_clinic)
    end

    it 'returns all associated imageables of any type' do
      expect(subject.imageables).to contain_exactly(chicken, vet_clinic)
    end
  end
end
