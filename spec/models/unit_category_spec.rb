require 'rails_helper'

RSpec.describe UnitCategory, type: :model do
  subject { build(:unit_category) }

  describe 'associations' do
    it { is_expected.to belong_to(:parent).class_name('UnitCategory').optional }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_uniqueness_of(:name) }
    it { is_expected.to validate_length_of(:name).is_at_least(1).is_at_most(255) }
  end
end
