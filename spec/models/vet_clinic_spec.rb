require 'rails_helper'

RSpec.describe VetClinic, type: :model do
  subject { build(:vet_clinic, name: 'Test Clinic', address: '123 Test St', city:) }

  let(:city) { create(:city) }

  describe 'validations' do
    it { is_expected.to be_valid }

    context 'when name is nil' do
      before { subject.name = nil }

      it { is_expected.not_to be_valid }

      it 'adds an error on name' do
        subject.valid?
        expect(subject.errors[:name]).to include("can't be blank")
      end
    end

    context 'when address is nil' do
      before { subject.address = nil }

      it { is_expected.not_to be_valid }

      it 'adds an error on address' do
        subject.valid?
        expect(subject.errors[:address]).to include("can't be blank")
      end
    end

    context 'when name and address combination is not unique within the same city' do
      before { create(:vet_clinic, name: 'Test Clinic', address: '123 Test St', city:) }

      it { is_expected.not_to be_valid }

      it 'adds an error on name' do
        subject.valid?
        expect(subject.errors[:name]).to include('with this address already exists in this city')
      end
    end
  end

  describe 'associations' do
    it { is_expected.to belong_to(:city) }
    it { is_expected.to have_many(:vet_visits).dependent(:destroy) }
    it { is_expected.to have_many(:chickens).through(:vet_visits) }
  end

  describe 'callbacks' do
    it 'strips whitespace from name and address before validation' do
      subject.name = '  Test Clinic  '
      subject.address = '  123 Test St  '
      subject.valid?
      expect(subject.name).to eq('Test Clinic')
      expect(subject.address).to eq('123 Test St')
    end
  end
end
