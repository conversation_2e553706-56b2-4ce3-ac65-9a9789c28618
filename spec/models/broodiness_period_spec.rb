require 'rails_helper'

RSpec.describe BroodinessPeriod, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:chicken) }
  end

  describe 'validations' do
    it 'validates that chicken is female' do
      broodiness_period = build(:broodiness_period, chicken: create(:chicken, gender: Gender.male))

      expect(broodiness_period).not_to be_valid
      expect(broodiness_period.errors[:chicken]).to include('must be female')
    end
  end
end
