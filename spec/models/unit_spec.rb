require 'rails_helper'

RSpec.describe Unit, type: :model do
  subject { build(:unit) }

  describe 'associations' do
    it { is_expected.to belong_to(:unit_category) }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_uniqueness_of(:name) }
    it { is_expected.to validate_length_of(:name).is_at_least(1).is_at_most(255) }
    it { is_expected.to validate_length_of(:sign).is_at_most(10) }
    it { is_expected.to validate_length_of(:description).is_at_most(5000) }
  end
end
