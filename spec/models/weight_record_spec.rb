require 'rails_helper'

RSpec.describe WeightRecord, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:entity) }
  end

  describe 'validations' do
    it { is_expected.to validate_numericality_of(:value).is_greater_than_or_equal_to(0) }
    it { is_expected.to validate_presence_of(:value) }
  end

  describe '#display_value' do
    it 'returns the value in grams if less than 1000' do
      weight_record = described_class.new(value: 500)
      expect(weight_record.display_value).to eq(500)
    end

    it 'returns the value in kilograms if 1000 or more' do
      weight_record = described_class.new(value: 1500)
      expect(weight_record.display_value).to eq(1.5)
    end
  end

  describe '#display_unit' do
    it "returns 'g' if the value is less than 1000" do
      weight_record = described_class.new(value: 500)
      expect(weight_record.display_unit).to eq('g')
    end

    it "returns 'kg' if the value is 1000 or more" do
      weight_record = described_class.new(value: 1500)
      expect(weight_record.display_unit).to eq('kg')
    end
  end
end
