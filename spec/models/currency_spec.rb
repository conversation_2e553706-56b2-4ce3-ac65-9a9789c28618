require 'rails_helper'

RSpec.describe Currency, type: :model do
  subject { build(:currency) }

  describe 'validations' do
    it { is_expected.to be_valid }
    it { is_expected.to validate_presence_of(:code).allow_nil }
    it { is_expected.to validate_length_of(:code).is_at_least(1).is_at_most(255).allow_nil }

    it { is_expected.to validate_presence_of(:name).allow_nil }
    it { is_expected.to validate_length_of(:name).is_at_least(1).is_at_most(255).allow_nil }

    it { is_expected.to validate_presence_of(:sign).allow_nil }
    it { is_expected.to validate_length_of(:sign).is_at_least(1).is_at_most(255).allow_nil }
  end
end
