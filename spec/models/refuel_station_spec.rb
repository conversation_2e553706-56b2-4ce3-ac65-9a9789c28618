require 'rails_helper'

RSpec.describe RefuelStation, type: :model do
  subject { build(:refuel_station) }

  describe 'associations' do
    it { is_expected.to belong_to(:brand).class_name('Persona') }
    it { is_expected.to belong_to(:city).optional }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:nickname).allow_nil }
    it { is_expected.to validate_uniqueness_of(:nickname).allow_nil }
    it { is_expected.to validate_length_of(:address).is_at_least(3).is_at_most(1000).allow_nil }
  end
end
