require 'rails_helper'

RSpec.describe PersonaType, type: :model do
  subject { build(:persona_type) }

  describe 'associations' do
    it { is_expected.to belong_to(:parent).optional(true).class_name('PersonaType') }

    it {
      expect(subject).to have_many(:children).class_name('PersonaType').with_foreign_key('parent_id').dependent(:destroy)
    }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:title) }
    it { is_expected.to validate_uniqueness_of(:title) }
    it { is_expected.to validate_length_of(:title).is_at_least(1).is_at_most(256) }
  end
end
