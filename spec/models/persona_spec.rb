require 'rails_helper'

RSpec.describe Persona, type: :model do
  subject { build(:persona) }

  describe 'associations' do
    it { is_expected.to belong_to(:alias_for).optional(true).class_name('User') }
    it { is_expected.to belong_to(:persona_type).optional(true) }
    it { is_expected.to belong_to(:relationship).optional(true) }
    it { is_expected.to have_many(:purchases).class_name('PurchaseEvent').with_foreign_key('buyer_id') }
    it { is_expected.to have_many(:sells).class_name('PurchaseEvent').with_foreign_key('seller_id') }
    it { is_expected.to have_and_belong_to_many(:documents).inverse_of(:holders) }
  end

  describe 'validations' do
    context 'when alias_for is present' do
      let(:persona) { build(:persona, alias_for: build(:user)) }

      it 'validates absence of persona_type' do
        persona.persona_type = build(:persona_type)
        expect(persona).not_to be_valid
        expect(persona.errors[:persona_type]).to include('cannot be set if this is an alias for a user')
      end

      it 'validates absence of relationship' do
        persona.relationship = build(:relationship)
        expect(persona).not_to be_valid
        expect(persona.errors[:relationship]).to include('cannot be set if this is an alias for a user')
      end
    end

    context 'when alias_for is nil and persona_type is nil' do
      let(:persona) { build(:persona, alias_for: nil, persona_type: nil) }

      it 'validates presence of relationship' do
        persona.relationship = nil
        expect(persona).not_to be_valid
        expect(persona.errors[:relationship]).to include('must be present when the type is not specified.')
      end
    end
  end
end
