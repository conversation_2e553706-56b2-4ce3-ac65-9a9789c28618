require 'rails_helper'

RSpec.describe BroodinessPeriodsController, type: :routing do
  describe 'routing' do
    it 'routes to #index' do
      expect(get: '/broodiness_periods').to route_to('broodiness_periods#index')
    end

    it 'routes to #new' do
      expect(get: '/broodiness_periods/new').to route_to('broodiness_periods#new')
    end

    it 'routes to #show' do
      expect(get: '/broodiness_periods/1').to route_to('broodiness_periods#show', id: '1')
    end

    it 'routes to #edit' do
      expect(get: '/broodiness_periods/1/edit').to route_to('broodiness_periods#edit', id: '1')
    end

    it 'routes to #create' do
      expect(post: '/broodiness_periods').to route_to('broodiness_periods#create')
    end

    it 'routes to #update via PUT' do
      expect(put: '/broodiness_periods/1').to route_to('broodiness_periods#update', id: '1')
    end

    it 'routes to #update via PATCH' do
      expect(patch: '/broodiness_periods/1').to route_to('broodiness_periods#update', id: '1')
    end

    it 'routes to #destroy' do
      expect(delete: '/broodiness_periods/1').to route_to('broodiness_periods#destroy', id: '1')
    end
  end
end
