#!/usr/bin/env sh

# Function to display a spinning icon
spin() {
  local -r chars='/-\|'
  local i=1
  while ((i < 100)); do
    printf "\r[${chars:i++%${#chars}:1}] Please wait..."
    sleep 0.1
  done
}

./_up

echo 'This command will destroy existing database and crreate one from scratch an populate it with default data.'
read -p "Do you wish to continue? (y/n): " choice

if [[ $choice == "y" || $choice == "Y" ]]; then
  # Save the cursor position
  tput sc

  # Start the spinner
  spin &

  # Perform the sleep delay
  sleep 10

  # Restore the cursor position
  tput rc

  echo -e "\r"  # Clear the spinning icon

  docker compose exec web sh -c 'rails db:drop db:create db:migrate db:seed'
else
  echo "Script execution canceled."
  exit 0
fi
