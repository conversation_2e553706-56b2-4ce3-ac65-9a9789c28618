require 'test_helper'

class EggTest < ActiveSupport::TestCase
  test 'content_health has to be between 0 and 1' do
    assert_raise do
      egg = new Egg content_health: -0.01
      egg.save
    end

    assert_raise do
      egg = new Egg content_health: 1.01
      egg.save
    end
  end

  test 'skin_health has to be between 0 and 1' do
    assert_raise do
      egg = new Egg skin_health: -0.01
      egg.save
    end

    assert_raise do
      egg = new Egg skin_health: 1.01
      egg.save
    end
  end

  test 'shape_health has to be between 0 and 1' do
    assert_raise do
      egg = new Egg shape_health: -0.01
      egg.save
    end

    assert_raise do
      egg = new Egg shape_health: 1.01
      egg.save
    end
  end

  test 'An egg cannot be laid by a chicken neither born nor joined, yet!' do
    egg = Egg.new

    egg.chickens << Chicken.new(name: 'X', gender: Gender.female, birth_day: Date.tomorrow)
    assert_not egg.valid?

    egg.chickens.clear

    egg.chickens << Chicken.new(name: 'Y', gender: Gender.female, join_day: Date.tomorrow)
    assert_not egg.valid?
  end

  test 'An egg cannot be laid by a chicken who has left us!' do
    egg = Egg.new

    egg.chickens << Chicken.new(name: 'X', gender: Gender.female, death_day: Date.yesterday)
    assert_not egg.valid?

    egg.chickens.clear

    egg.chickens << Chicken.new(name: 'Y', gender: Gender.female, leave_day: Date.yesterday)
    assert_not egg.valid?
  end
end
