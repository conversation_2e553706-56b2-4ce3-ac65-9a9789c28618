require 'application_system_test_case'

class RefuelEventsTest < ApplicationSystemTestCase
  setup do
    @refuel_event = refuel_events(:one)
  end

  test 'visiting the index' do
    visit refuel_events_url
    assert_selector 'h1', text: 'Refuel events'
  end

  test 'should create refuel event' do
    visit refuel_events_url
    click_on 'New refuel event'

    fill_in 'Amount', with: @refuel_event.amount
    fill_in 'Currency', with: @refuel_event.currency_id
    fill_in 'Paid price', with: @refuel_event.paid_price
    fill_in 'Refuel station', with: @refuel_event.refuel_station_id
    fill_in 'Refuled at', with: @refuel_event.refuled_at
    fill_in 'Unit', with: @refuel_event.unit_id
    click_on 'Create Refuel event'

    assert_text 'Refuel event was successfully created'
    click_on 'Back'
  end

  test 'should update Refuel event' do
    visit refuel_event_url(@refuel_event)
    click_on 'Edit this refuel event', match: :first

    fill_in 'Amount', with: @refuel_event.amount
    fill_in 'Currency', with: @refuel_event.currency_id
    fill_in 'Paid price', with: @refuel_event.paid_price
    fill_in 'Refuel station', with: @refuel_event.refuel_station_id
    fill_in 'Refuled at', with: @refuel_event.refuled_at
    fill_in 'Unit', with: @refuel_event.unit_id
    click_on 'Update Refuel event'

    assert_text 'Refuel event was successfully updated'
    click_on 'Back'
  end

  test 'should destroy Refuel event' do
    visit refuel_event_url(@refuel_event)
    click_on 'Destroy this refuel event', match: :first

    assert_text 'Refuel event was successfully destroyed'
  end
end
