require 'application_system_test_case'

class GiftEventsTest < ApplicationSystemTestCase
  setup do
    @gift_event = gift_events(:one)
  end

  test 'visiting the index' do
    visit gift_events_url
    assert_selector 'h1', text: 'Gift events'
  end

  test 'should create gift event' do
    visit gift_events_url
    click_on 'New gift event'

    fill_in 'Date', with: @gift_event.date
    fill_in 'Persona', with: @gift_event.persona_id
    click_on 'Create Gift event'

    assert_text 'Gift event was successfully created'
    click_on 'Back'
  end

  test 'should update Gift event' do
    visit gift_event_url(@gift_event)
    click_on 'Edit this gift event', match: :first

    fill_in 'Date', with: @gift_event.date
    fill_in 'Persona', with: @gift_event.persona_id
    click_on 'Update Gift event'

    assert_text 'Gift event was successfully updated'
    click_on 'Back'
  end

  test 'should destroy Gift event' do
    visit gift_event_url(@gift_event)
    click_on 'Destroy this gift event', match: :first

    assert_text 'Gift event was successfully destroyed'
  end
end
