require 'application_system_test_case'

class PersonasTest < ApplicationSystemTestCase
  setup do
    @persona = personas(:one)
  end

  test 'visiting the index' do
    visit personas_url
    assert_selector 'h1', text: 'Personas'
  end

  test 'should create persona' do
    visit personas_url
    click_on 'New persona'

    fill_in 'Nickname', with: @persona.nickname
    fill_in 'Relationship', with: @persona.relationship_id
    click_on 'Create Persona'

    assert_text '<PERSON><PERSON> was successfully created'
    click_on 'Back'
  end

  test 'should update <PERSON><PERSON>' do
    visit persona_url(@persona)
    click_on 'Edit this persona', match: :first

    fill_in 'Nickname', with: @persona.nickname
    fill_in 'Relationship', with: @persona.relationship_id
    click_on 'Update Persona'

    assert_text '<PERSON>a was successfully updated'
    click_on 'Back'
  end

  test 'should destroy <PERSON><PERSON>' do
    visit persona_url(@persona)
    click_on 'Destroy this persona', match: :first

    assert_text '<PERSON><PERSON> was successfully destroyed'
  end
end
