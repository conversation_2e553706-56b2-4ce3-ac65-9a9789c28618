require 'application_system_test_case'

class PersonaTypesTest < ApplicationSystemTestCase
  setup do
    @persona_type = persona_types(:one)
  end

  test 'visiting the index' do
    visit persona_types_url
    assert_selector 'h1', text: 'Persona types'
  end

  test 'should create persona type' do
    visit persona_types_url
    click_on 'New persona type'

    fill_in 'Parent', with: @persona_type.parent_id
    fill_in 'Title', with: @persona_type.title
    click_on 'Create Persona type'

    assert_text 'Persona type was successfully created'
    click_on 'Back'
  end

  test 'should update Persona type' do
    visit persona_type_url(@persona_type)
    click_on 'Edit this persona type', match: :first

    fill_in 'Parent', with: @persona_type.parent_id
    fill_in 'Title', with: @persona_type.title
    click_on 'Update Persona type'

    assert_text 'Persona type was successfully updated'
    click_on 'Back'
  end

  test 'should destroy Persona type' do
    visit persona_type_url(@persona_type)
    click_on 'Destroy this persona type', match: :first

    assert_text 'Persona type was successfully destroyed'
  end
end
