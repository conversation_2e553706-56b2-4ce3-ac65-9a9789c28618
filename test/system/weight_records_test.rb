require 'application_system_test_case'

class WeightRecordsTest < ApplicationSystemTestCase
  setup do
    @weight_record = weight_records(:one)
  end

  test 'visiting the index' do
    visit weight_records_url
    assert_selector 'h1', text: 'Weight records'
  end

  test 'should create weight record' do
    visit weight_records_url
    click_on 'New weight record'

    fill_in 'Date', with: @weight_record.date
    fill_in 'Entity', with: @weight_record.entity_id
    fill_in 'Entity type', with: @weight_record.entity_type
    fill_in 'Value', with: @weight_record.value
    click_on 'Create Weight record'

    assert_text 'Weight record was successfully created'
    click_on 'Back'
  end

  test 'should update Weight record' do
    visit weight_record_url(@weight_record)
    click_on 'Edit this weight record', match: :first

    fill_in 'Date', with: @weight_record.date
    fill_in 'Entity', with: @weight_record.entity_id
    fill_in 'Entity type', with: @weight_record.entity_type
    fill_in 'Value', with: @weight_record.value
    click_on 'Update Weight record'

    assert_text 'Weight record was successfully updated'
    click_on 'Back'
  end

  test 'should destroy Weight record' do
    visit weight_record_url(@weight_record)
    click_on 'Destroy this weight record', match: :first

    assert_text 'Weight record was successfully destroyed'
  end
end
