require 'application_system_test_case'

class VetVisitsTest < ApplicationSystemTestCase
  setup do
    @vet_visit = vet_visits(:one)
  end

  test 'visiting the index' do
    visit vet_visits_url
    assert_selector 'h1', text: 'Vet visits'
  end

  test 'should create vet visit' do
    visit vet_visits_url
    click_on 'New vet visit'

    check 'At home' if @vet_visit.at_home
    fill_in 'Chicken', with: @vet_visit.chicken_id
    fill_in 'Date', with: @vet_visit.date
    fill_in 'Doctor verdict', with: @vet_visit.doctor_verdict
    check 'Has medicine routine' if @vet_visit.has_medicine_routine
    check 'Injection in place' if @vet_visit.injection_in_place
    check 'Medicine in place' if @vet_visit.medicine_in_place
    check 'Needs second visit' if @vet_visit.needs_second_visit
    fill_in 'Prescription', with: @vet_visit.prescription
    fill_in 'Severity', with: @vet_visit.severity
    fill_in 'Situation description', with: @vet_visit.situation_description
    fill_in 'Vet clinic', with: @vet_visit.vet_clinic_id
    fill_in 'Vet', with: @vet_visit.vet_id
    click_on 'Create Vet visit'

    assert_text 'Vet visit was successfully created'
    click_on 'Back'
  end

  test 'should update Vet visit' do
    visit vet_visit_url(@vet_visit)
    click_on 'Edit this vet visit', match: :first

    check 'At home' if @vet_visit.at_home
    fill_in 'Chicken', with: @vet_visit.chicken_id
    fill_in 'Date', with: @vet_visit.date
    fill_in 'Doctor verdict', with: @vet_visit.doctor_verdict
    check 'Has medicine routine' if @vet_visit.has_medicine_routine
    check 'Injection in place' if @vet_visit.injection_in_place
    check 'Medicine in place' if @vet_visit.medicine_in_place
    check 'Needs second visit' if @vet_visit.needs_second_visit
    fill_in 'Prescription', with: @vet_visit.prescription
    fill_in 'Severity', with: @vet_visit.severity
    fill_in 'Situation description', with: @vet_visit.situation_description
    fill_in 'Vet clinic', with: @vet_visit.vet_clinic_id
    fill_in 'Vet', with: @vet_visit.vet_id
    click_on 'Update Vet visit'

    assert_text 'Vet visit was successfully updated'
    click_on 'Back'
  end

  test 'should destroy Vet visit' do
    visit vet_visit_url(@vet_visit)
    click_on 'Destroy this vet visit', match: :first

    assert_text 'Vet visit was successfully destroyed'
  end
end
