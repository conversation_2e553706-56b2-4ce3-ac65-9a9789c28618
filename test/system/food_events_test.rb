require 'application_system_test_case'

class FoodEventsTest < ApplicationSystemTestCase
  setup do
    @food_event = food_events(:one)
  end

  test 'visiting the index' do
    visit food_events_url
    assert_selector 'h1', text: 'Food events'
  end

  test 'should create food event' do
    visit food_events_url
    click_on 'New food event'

    fill_in 'Cooker', with: @food_event.cooker_id
    fill_in 'Date', with: @food_event.date
    fill_in 'Food type', with: @food_event.food_type_id
    click_on 'Create Food event'

    assert_text 'Food event was successfully created'
    click_on 'Back'
  end

  test 'should update Food event' do
    visit food_event_url(@food_event)
    click_on 'Edit this food event', match: :first

    fill_in 'Cooker', with: @food_event.cooker_id
    fill_in 'Date', with: @food_event.date
    fill_in 'Food type', with: @food_event.food_type_id
    click_on 'Update Food event'

    assert_text 'Food event was successfully updated'
    click_on 'Back'
  end

  test 'should destroy Food event' do
    visit food_event_url(@food_event)
    click_on 'Destroy this food event', match: :first

    assert_text 'Food event was successfully destroyed'
  end
end
