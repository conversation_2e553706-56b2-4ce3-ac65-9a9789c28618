require 'application_system_test_case'

class UnitCategoriesTest < ApplicationSystemTestCase
  setup do
    @unit_category = unit_categories(:one)
  end

  test 'visiting the index' do
    visit unit_categories_url
    assert_selector 'h1', text: 'Unit categories'
  end

  test 'should create unit category' do
    visit unit_categories_url
    click_on 'New unit category'

    fill_in 'Description', with: @unit_category.description
    fill_in 'Name', with: @unit_category.name
    fill_in 'Parent', with: @unit_category.parent_id
    click_on 'Create Unit category'

    assert_text 'Unit category was successfully created'
    click_on 'Back'
  end

  test 'should update Unit category' do
    visit unit_category_url(@unit_category)
    click_on 'Edit this unit category', match: :first

    fill_in 'Description', with: @unit_category.description
    fill_in 'Name', with: @unit_category.name
    fill_in 'Parent', with: @unit_category.parent_id
    click_on 'Update Unit category'

    assert_text 'Unit category was successfully updated'
    click_on 'Back'
  end

  test 'should destroy Unit category' do
    visit unit_category_url(@unit_category)
    click_on 'Destroy this unit category', match: :first

    assert_text 'Unit category was successfully destroyed'
  end
end
