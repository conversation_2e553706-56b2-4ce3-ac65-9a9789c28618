require 'application_system_test_case'

class VetClinicsTest < ApplicationSystemTestCase
  setup do
    @vet_clinic = vet_clinics(:one)
  end

  test 'visiting the index' do
    visit vet_clinics_url
    assert_selector 'h1', text: 'Vet clinics'
  end

  test 'should create vet clinic' do
    visit vet_clinics_url
    click_on 'New vet clinic'

    fill_in 'Address', with: @vet_clinic.address
    fill_in 'City', with: @vet_clinic.city_id
    fill_in 'Name', with: @vet_clinic.name
    click_on 'Create Vet clinic'

    assert_text 'Vet clinic was successfully created'
    click_on 'Back'
  end

  test 'should update Vet clinic' do
    visit vet_clinic_url(@vet_clinic)
    click_on 'Edit this vet clinic', match: :first

    fill_in 'Address', with: @vet_clinic.address
    fill_in 'City', with: @vet_clinic.city_id
    fill_in 'Name', with: @vet_clinic.name
    click_on 'Update Vet clinic'

    assert_text 'Vet clinic was successfully updated'
    click_on 'Back'
  end

  test 'should destroy Vet clinic' do
    visit vet_clinic_url(@vet_clinic)
    click_on 'Destroy this vet clinic', match: :first

    assert_text 'Vet clinic was successfully destroyed'
  end
end
