require 'application_system_test_case'

class RefuelStationsTest < ApplicationSystemTestCase
  setup do
    @refuel_station = refuel_stations(:one)
  end

  test 'visiting the index' do
    visit refuel_stations_url
    assert_selector 'h1', text: 'Refuel stations'
  end

  test 'should create refuel station' do
    visit refuel_stations_url
    click_on 'New refuel station'

    fill_in 'Brnad', with: @refuel_station.brand_id
    fill_in 'Latitude', with: @refuel_station.latitude
    fill_in 'Longitude', with: @refuel_station.longitude
    fill_in 'Nickname', with: @refuel_station.nickname
    click_on 'Create Refuel station'

    assert_text 'Refuel station was successfully created'
    click_on 'Back'
  end

  test 'should update Refuel station' do
    visit refuel_station_url(@refuel_station)
    click_on 'Edit this refuel station', match: :first

    fill_in 'Brnad', with: @refuel_station.brand_id
    fill_in 'Latitude', with: @refuel_station.latitude
    fill_in 'Longitude', with: @refuel_station.longitude
    fill_in 'Nickname', with: @refuel_station.nickname
    click_on 'Update Refuel station'

    assert_text 'Refuel station was successfully updated'
    click_on 'Back'
  end

  test 'should destroy Refuel station' do
    visit refuel_station_url(@refuel_station)
    click_on 'Destroy this refuel station', match: :first

    assert_text 'Refuel station was successfully destroyed'
  end
end
