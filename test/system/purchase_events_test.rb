require 'application_system_test_case'

class PurchaseEventsTest < ApplicationSystemTestCase
  setup do
    @purchase_event = purchase_events(:one)
  end

  test 'visiting the index' do
    visit purchase_events_url
    assert_selector 'h1', text: 'Purchase events'
  end

  test 'should create purchase event' do
    visit purchase_events_url
    click_on 'New purchase event'

    fill_in 'Buyer', with: @purchase_event.buyer_id
    fill_in 'Purchased at', with: @purchase_event.purchased_at
    fill_in 'Seller', with: @purchase_event.seller_id
    click_on 'Create Purchase event'

    assert_text 'Purchase event was successfully created'
    click_on 'Back'
  end

  test 'should update Purchase event' do
    visit purchase_event_url(@purchase_event)
    click_on 'Edit this purchase event', match: :first

    fill_in 'Buyer', with: @purchase_event.buyer_id
    fill_in 'Purchased at', with: @purchase_event.purchased_at
    fill_in 'Seller', with: @purchase_event.seller_id
    click_on 'Update Purchase event'

    assert_text 'Purchase event was successfully updated'
    click_on 'Back'
  end

  test 'should destroy Purchase event' do
    visit purchase_event_url(@purchase_event)
    click_on 'Destroy this purchase event', match: :first

    assert_text 'Purchase event was successfully destroyed'
  end
end
