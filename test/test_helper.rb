ENV['RAILS_ENV'] ||= 'test'
require_relative '../config/environment'
require 'rails/test_help'
require 'devise/test/integration_helpers'

module ActiveSupport
  class TestCase
    include Devise::Test::IntegrationHelpers

    # Run tests in parallel with specified workers
    parallelize(workers: :number_of_processors)

    # Setup all fixtures in test/fixtures/*.yml for all tests in alphabetical order.
    fixtures :all

    # Add more helper methods to be used by all tests here...
    protected

    def admin = User.find_by(role: User.roles[:admin])
    def contributor = User.find_by(role: User.roles[:contributor])
    def regular = User.find_by(role: User.roles[:regular])
  end
end
