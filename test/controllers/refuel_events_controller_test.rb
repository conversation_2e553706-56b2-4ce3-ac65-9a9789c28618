require 'test_helper'

class RefuelEventsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @refuel_event = refuel_events(:one)
  end

  test 'should get index' do
    get refuel_events_url
    assert_response :success
  end

  test 'should get new' do
    get new_refuel_event_url
    assert_response :success
  end

  test 'should create refuel_event' do
    assert_difference('RefuelEvent.count') do
      post refuel_events_url,
           params: {
             refuel_event: {
               amount: @refuel_event.amount,
               currency_id: @refuel_event.currency_id,
               paid_price: @refuel_event.paid_price,
               refuel_station_id: @refuel_event.refuel_station_id,
               refuled_at: @refuel_event.refuled_at,
               unit_id: @refuel_event.unit_id
             }
           }
    end

    assert_redirected_to refuel_event_url(RefuelEvent.last)
  end

  test 'should show refuel_event' do
    get refuel_event_url(@refuel_event)
    assert_response :success
  end

  test 'should get edit' do
    get edit_refuel_event_url(@refuel_event)
    assert_response :success
  end

  test 'should update refuel_event' do
    patch refuel_event_url(@refuel_event),
          params: {
            refuel_event: {
              amount: @refuel_event.amount,
              currency_id: @refuel_event.currency_id,
              paid_price: @refuel_event.paid_price,
              refuel_station_id: @refuel_event.refuel_station_id,
              refuled_at: @refuel_event.refuled_at,
              unit_id: @refuel_event.unit_id
            }
          }
    assert_redirected_to refuel_event_url(@refuel_event)
  end

  test 'should destroy refuel_event' do
    assert_difference('RefuelEvent.count', -1) do
      delete refuel_event_url(@refuel_event)
    end

    assert_redirected_to refuel_events_url
  end
end
