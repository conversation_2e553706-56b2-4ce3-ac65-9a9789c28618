require 'test_helper'

class SellEventsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @sell_event = sell_events(:one)
  end

  test 'should get index' do
    get sell_events_url
    assert_response :success
  end

  test 'should get new' do
    get new_sell_event_url
    assert_response :success
  end

  test 'should create sell_event' do
    assert_difference('SellEvent.count') do
      post sell_events_url, params: { sell_event: { date: @sell_event.date, persona_id: @sell_event.persona_id } }
    end

    assert_redirected_to sell_event_url(SellEvent.last)
  end

  test 'should show sell_event' do
    get sell_event_url(@sell_event)
    assert_response :success
  end

  test 'should get edit' do
    get edit_sell_event_url(@sell_event)
    assert_response :success
  end

  test 'should update sell_event' do
    patch sell_event_url(@sell_event),
          params: { sell_event: { date: @sell_event.date, persona_id: @sell_event.persona_id } }
    assert_redirected_to sell_event_url(@sell_event)
  end

  test 'should destroy sell_event' do
    assert_difference('SellEvent.count', -1) do
      delete sell_event_url(@sell_event)
    end

    assert_redirected_to sell_events_url
  end
end
