require 'test_helper'

class VetVisitsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @vet_visit = vet_visits(:one)
  end

  test 'should get index' do
    get vet_visits_url
    assert_response :success
  end

  test 'should get new' do
    get new_vet_visit_url
    assert_response :success
  end

  test 'should create vet_visit' do
    assert_difference('VetVisit.count') do
      post vet_visits_url,
           params: { vet_visit: { at_home: @vet_visit.at_home,
                                  chicken_id: @vet_visit.chicken_id,
                                  date: @vet_visit.date,
                                  doctor_verdict: @vet_visit.doctor_verdict,
                                  has_medicine_routine: @vet_visit.has_medicine_routine,
                                  injection_in_place: @vet_visit.injection_in_place,
                                  medicine_in_place: @vet_visit.medicine_in_place,
                                  needs_second_visit: @vet_visit.needs_second_visit,
                                  prescription: @vet_visit.prescription,
                                  severity: @vet_visit.severity,
                                  situation_description: @vet_visit.situation_description,
                                  vet_clinic_id: @vet_visit.vet_clinic_id,
                                  vet_id: @vet_visit.vet_id
                                }
                   }
    end

    assert_redirected_to vet_visit_url(VetVisit.last)
  end

  test 'should show vet_visit' do
    get vet_visit_url(@vet_visit)
    assert_response :success
  end

  test 'should get edit' do
    get edit_vet_visit_url(@vet_visit)
    assert_response :success
  end

  test 'should update vet_visit' do
    patch vet_visit_url(@vet_visit),
          params: { vet_visit: { at_home: @vet_visit.at_home,
                                 chicken_id: @vet_visit.chicken_id,
                                 date: @vet_visit.date,
                                 doctor_verdict: @vet_visit.doctor_verdict,
                                 has_medicine_routine: @vet_visit.has_medicine_routine,
                                 injection_in_place: @vet_visit.injection_in_place,
                                 medicine_in_place: @vet_visit.medicine_in_place,
                                 needs_second_visit: @vet_visit.needs_second_visit,
                                 prescription: @vet_visit.prescription,
                                 severity: @vet_visit.severity,
                                 situation_description: @vet_visit.situation_description,
                                 vet_clinic_id: @vet_visit.vet_clinic_id,
                                 vet_id: @vet_visit.vet_id
                                }
                  }
    assert_redirected_to vet_visit_url(@vet_visit)
  end

  test 'should destroy vet_visit' do
    assert_difference('VetVisit.count', -1) do
      delete vet_visit_url(@vet_visit)
    end

    assert_redirected_to vet_visits_url
  end
end
