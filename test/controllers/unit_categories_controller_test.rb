require 'test_helper'

class UnitCategoriesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @unit_category = unit_categories(:one)
  end

  test 'should get index' do
    get unit_categories_url
    assert_response :success
  end

  test 'should get new' do
    get new_unit_category_url
    assert_response :success
  end

  test 'should create unit_category' do
    assert_difference('UnitCategory.count') do
      post unit_categories_url,
           params: { unit_category: { description: @unit_category.description,
                                      name: @unit_category.name,
                                      parent_id: @unit_category.parent_id
                                    }
                   }
    end

    assert_redirected_to unit_category_url(UnitCategory.last)
  end

  test 'should show unit_category' do
    get unit_category_url(@unit_category)
    assert_response :success
  end

  test 'should get edit' do
    get edit_unit_category_url(@unit_category)
    assert_response :success
  end

  test 'should update unit_category' do
    patch unit_category_url(@unit_category),
          params: { unit_category: { description: @unit_category.description,
                                     name: @unit_category.name,
                                     parent_id: @unit_category.parent_id
                                   }
                  }
    assert_redirected_to unit_category_url(@unit_category)
  end

  test 'should destroy unit_category' do
    assert_difference('UnitCategory.count', -1) do
      delete unit_category_url(@unit_category)
    end

    assert_redirected_to unit_categories_url
  end
end
