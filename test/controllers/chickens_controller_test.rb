require 'test_helper'

class ChickensControllerTest < ActionDispatch::IntegrationTest
  setup do
    @chicken = chickens(:one)
    sign_in admin
  end

  test 'should get index' do
    get chickens_url
    assert_response :success
  end

  test 'should get new' do
    get new_chicken_url
    assert_response :success
  end

  test 'should create chicken' do
    assert_difference('Chicken.count') do
      post chickens_url, params: {
        chicken: {
          birth_day: @chicken.birth_day,
          death_day: @chicken.death_day,
          gender_id: @chicken.gender_id,
          join_day: @chicken.join_day,
          leave_day: @chicken.leave_day,
          name: 'Some random chicken name'
        }
      }
    end

    assert_redirected_to chicken_url(Chicken.last)
  end

  test 'should show chicken' do
    get chicken_url(@chicken)
    assert_response :success
  end

  test 'should get edit' do
    get edit_chicken_url(@chicken)
    assert_response :success
  end

  test 'should update chicken' do
    patch chicken_url(@chicken), params: {
      chicken: {
        birth_day: @chicken.birth_day,
        death_day: @chicken.death_day,
        gender_id: @chicken.gender_id,
        join_day: @chicken.join_day,
        leave_day: @chicken.leave_day,
        name: 'Some other random chicken name'
      }
    }
    assert_redirected_to chicken_url(@chicken)
  end

  test 'should destroy chicken' do
    assert_difference('Chicken.count', -1) do
      delete chicken_url(@chicken)
    end

    assert_redirected_to chickens_url
  end
end
