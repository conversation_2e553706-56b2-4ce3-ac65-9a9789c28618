require 'test_helper'

class FoodEventsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @food_event = food_events(:one)
  end

  test 'should get index' do
    get food_events_url
    assert_response :success
  end

  test 'should get new' do
    get new_food_event_url
    assert_response :success
  end

  test 'should create food_event' do
    assert_difference('FoodEvent.count') do
      post food_events_url,
           params: {
             food_event: {
               cooker_id: @food_event.cooker_id,
               date: @food_event.date,
               food_type_id: @food_event.food_type_id
             }
           }
    end

    assert_redirected_to food_event_url(FoodEvent.last)
  end

  test 'should show food_event' do
    get food_event_url(@food_event)
    assert_response :success
  end

  test 'should get edit' do
    get edit_food_event_url(@food_event)
    assert_response :success
  end

  test 'should update food_event' do
    patch food_event_url(@food_event),
          params: {
            food_event: {
              cooker_id: @food_event.cooker_id,
              date: @food_event.date,
              food_type_id: @food_event.food_type_id
            }
          }
    assert_redirected_to food_event_url(@food_event)
  end

  test 'should destroy food_event' do
    assert_difference('FoodEvent.count', -1) do
      delete food_event_url(@food_event)
    end

    assert_redirected_to food_events_url
  end
end
