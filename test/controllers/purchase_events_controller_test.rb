require 'test_helper'

class PurchaseEventsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @purchase_event = purchase_events(:one)
  end

  test 'should get index' do
    get purchase_events_url
    assert_response :success
  end

  test 'should get new' do
    get new_purchase_event_url
    assert_response :success
  end

  test 'should create purchase_event' do
    assert_difference('PurchaseEvent.count') do
      post purchase_events_url,
           params: {
             purchase_event: {
               buyer_id: @purchase_event.buyer_id,
               purchased_at: @purchase_event.purchased_at,
               seller_id: @purchase_event.seller_id
             }
           }
    end

    assert_redirected_to purchase_event_url(PurchaseEvent.last)
  end

  test 'should show purchase_event' do
    get purchase_event_url(@purchase_event)
    assert_response :success
  end

  test 'should get edit' do
    get edit_purchase_event_url(@purchase_event)
    assert_response :success
  end

  test 'should update purchase_event' do
    patch purchase_event_url(@purchase_event),
          params: {
            purchase_event: {
              buyer_id: @purchase_event.buyer_id,
              purchased_at: @purchase_event.purchased_at,
              seller_id: @purchase_event.seller_id
            }
          }
    assert_redirected_to purchase_event_url(@purchase_event)
  end

  test 'should destroy purchase_event' do
    assert_difference('PurchaseEvent.count', -1) do
      delete purchase_event_url(@purchase_event)
    end

    assert_redirected_to purchase_events_url
  end
end
