require 'test_helper'

class VetClinicsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @vet_clinic = vet_clinics(:one)
  end

  test 'should get index' do
    get vet_clinics_url
    assert_response :success
  end

  test 'should get new' do
    get new_vet_clinic_url
    assert_response :success
  end

  test 'should create vet_clinic' do
    assert_difference('VetClinic.count') do
      post vet_clinics_url,
           params: { vet_clinic: { address: @vet_clinic.address,
                                   city_id: @vet_clinic.city_id,
                                   name: @vet_clinic.name
                                 }
                   }
    end

    assert_redirected_to vet_clinic_url(VetClinic.last)
  end

  test 'should show vet_clinic' do
    get vet_clinic_url(@vet_clinic)
    assert_response :success
  end

  test 'should get edit' do
    get edit_vet_clinic_url(@vet_clinic)
    assert_response :success
  end

  test 'should update vet_clinic' do
    patch vet_clinic_url(@vet_clinic),
          params: { vet_clinic: { address: @vet_clinic.address, city_id: @vet_clinic.city_id, name: @vet_clinic.name } }
    assert_redirected_to vet_clinic_url(@vet_clinic)
  end

  test 'should destroy vet_clinic' do
    assert_difference('VetClinic.count', -1) do
      delete vet_clinic_url(@vet_clinic)
    end

    assert_redirected_to vet_clinics_url
  end
end
