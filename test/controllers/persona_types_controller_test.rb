require 'test_helper'

class PersonaTypesControllerTest < ActionDispatch::IntegrationTest
  setup do
    @persona_type = persona_types(:one)
  end

  test 'should get index' do
    get persona_types_url
    assert_response :success
  end

  test 'should get new' do
    get new_persona_type_url
    assert_response :success
  end

  test 'should create persona_type' do
    assert_difference('PersonaType.count') do
      post persona_types_url,
           params: { persona_type: { parent_id: @persona_type.parent_id, title: @persona_type.title } }
    end

    assert_redirected_to persona_type_url(PersonaType.last)
  end

  test 'should show persona_type' do
    get persona_type_url(@persona_type)
    assert_response :success
  end

  test 'should get edit' do
    get edit_persona_type_url(@persona_type)
    assert_response :success
  end

  test 'should update persona_type' do
    patch persona_type_url(@persona_type),
          params: { persona_type: { parent_id: @persona_type.parent_id, title: @persona_type.title } }
    assert_redirected_to persona_type_url(@persona_type)
  end

  test 'should destroy persona_type' do
    assert_difference('PersonaType.count', -1) do
      delete persona_type_url(@persona_type)
    end

    assert_redirected_to persona_types_url
  end
end
