require 'test_helper'

class GiftEventsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @gift_event = gift_events(:one)
  end

  test 'should get index' do
    get gift_events_url
    assert_response :success
  end

  test 'should get new' do
    get new_gift_event_url
    assert_response :success
  end

  test 'should create gift_event' do
    assert_difference('GiftEvent.count') do
      post gift_events_url, params: { gift_event: { date: @gift_event.date, persona_id: @gift_event.persona_id } }
    end

    assert_redirected_to gift_event_url(GiftEvent.last)
  end

  test 'should show gift_event' do
    get gift_event_url(@gift_event)
    assert_response :success
  end

  test 'should get edit' do
    get edit_gift_event_url(@gift_event)
    assert_response :success
  end

  test 'should update gift_event' do
    patch gift_event_url(@gift_event),
          params: { gift_event: { date: @gift_event.date, persona_id: @gift_event.persona_id } }
    assert_redirected_to gift_event_url(@gift_event)
  end

  test 'should destroy gift_event' do
    assert_difference('GiftEvent.count', -1) do
      delete gift_event_url(@gift_event)
    end

    assert_redirected_to gift_events_url
  end
end
