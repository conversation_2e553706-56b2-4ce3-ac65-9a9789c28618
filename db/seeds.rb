# rubocop:disable Layout/ExtraSpacing, Layout/LineLength
# This file should contain all the record creation needed to seed the database with its default values.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).
#
# Examples:
#
#   movies = Movie.create([{ name: "Star Wars" }, { name: "Lord of the Rings" }])
#   Character.create(name: "<PERSON>", movie: movies.first)

juan_jose = '<PERSON>rooof'

WeightRecord.destroy_all
Egg.destroy_all
Chicken.destroy_all

Chicken.create([
  { name: '<PERSON><PERSON>',  color: '#095113', birth_day: DateTime.new(2020,  8, 11, 10,  0,  0), gender: Gender.male },
  { name: '<PERSON><PERSON>',      color: '#065143', birth_day: DateTime.new(2020,  8, 11, 10,  0,  0) },
  { name: '<PERSON><PERSON><PERSON>',    color: '#F26419', birth_day: DateTime.new(2020,  8, 11, 10,  0,  0) },
  { name: '<PERSON><PERSON>',      color: '#00F2F2', birth_day: DateTime.new(2018, 11,  4,  9, 30, 15), death_day: DateTime.new(2023,  4, 18) },
  { name: 'Na<PERSON>',     color: '#BC69AA', birth_day: DateTime.new(2019, 11, 11, 16, 18, 20), death_day: DateTime.new(2023,  5, 19) },
  { name: 'Naser',     color: '#1C448E', birth_day: DateTime.new(2017, 11, 11,  8,  0,  0), death_day: DateTime.new(2022, 10,  1) },
  { name: 'Reshma',    color: '#662C91', birth_day: DateTime.new(2020,  8, 11, 10,  0,  0), death_day: DateTime.new(2021,  6,  6) },
  { name: 'Asal',      color: '#81171B',  join_day: DateTime.new(2023,  9, 15,  9, 30,  0) },
  { name: 'Bahar',     color: '#706C61',  join_day: DateTime.new(2023,  9, 15,  9, 30,  0), death_day: DateTime.new(2023,  8,  17) },
  { name: 'Baran',     color: '#809BCE',  join_day: DateTime.new(2023,  9, 15,  9, 30,  0) },
  { name: juan_jose,   color: '#63326E',  join_day: DateTime.new(2023,  9, 15,  9, 30,  0) },
  { name: 'Mahlagha',  color: '#F06C9B',  join_day: DateTime.new(2023,  9, 15,  9, 30,  0) },
  { name: 'Morvarid',  color: '#6610F2',  join_day: DateTime.new(2023,  9, 15,  9, 30,  0) },
  { name: 'Rontgen',   color: '#001021',  join_day: DateTime.new(2023,  9, 15,  9, 30,  0) },
  { name: 'Shahbagha', color: '#4C4C9D',  join_day: DateTime.new(2023,  9, 15,  9, 30,  0) },
])

Chicken.female.each do |chicken|
  first_day = chicken.birth_day.present? ? (chicken.birth_day + 6.months) : chicken.join_day
  last_day = (chicken.death_day || 1.second.ago) - 1.day
  egging_perdiod = first_day.midday.to_date..last_day.midday.to_date

  egging_perdiod.each do |day|
    next if rand < 0.3

    random_seconds = rand((8.hours)..(14.hours))
    laid_at_time = day.beginning_of_day + random_seconds

    egg = Egg.create(
      laid_at: laid_at_time,
      weight: rand(45..72),
      width: rand(32..44),
      height: rand(32..60)
    )

    egg.chickens << chicken if rand < 0.3

    WeightRecord.create(entity: chicken, date: day.midday, value: rand(900..2000)) if day.wday < 5 && rand < 0.05
  end
end

User.create([
  { email: '<EMAIL>',   password: '12345678', role: User.roles[:admin] },
  { email: '<EMAIL>', password: '12345678', role: User.roles[:contributor] },
  { email: '<EMAIL>',     password: '12345678', role: User.roles[:regular] },
])
# rubocop:enable Layout/ExtraSpacing, Layout/LineLength
