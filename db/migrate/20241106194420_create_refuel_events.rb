class CreateRefuelEvents < ActiveRecord::Migration[7.1]
  def change
    create_table :refuel_events do |t|
      t.references :refuel_station, null: false, foreign_key: true
      t.datetime :refuled_at, null: false
      t.decimal :amount, null: false
      t.decimal :paid_price, null: false
      t.references :currency, null: false, foreign_key: true
      t.references :unit, null: false, foreign_key: true

      t.timestamps
    end
  end
end
