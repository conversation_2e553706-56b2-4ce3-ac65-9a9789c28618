class CreateChickens < ActiveRecord::Migration[7.0]
  def change
    create_table :chickens do |t|
      t.string :name, null: false
      t.datetime :birth_day
      t.datetime :death_day
      t.datetime :join_day
      t.datetime :leave_day
      t.references :gender, null: false, foreign_key: true, default: Gender.female.id

      t.timestamps
    end

    add_index :chickens, 'lower(name)', unique: true
  end
end
