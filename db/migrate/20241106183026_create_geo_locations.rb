class CreateGeoLocations < ActiveRecord::Migration[7.1]
  def change
    create_table :geo_locations do |t|
      t.decimal :latitude, precision: 10, scale: 6
      t.decimal :longitude, precision: 10, scale: 6
      t.string :address
      t.string :google_maps_uri

      t.timestamps
    end

    add_index :geo_locations, %i[latitude longitude address], unique: true,
                                                              name: 'index_geo_locations_on_lat_lng_address'
  end
end
