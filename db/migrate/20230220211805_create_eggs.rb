class CreateEggs < ActiveRecord::Migration[7.0]
  def change
    create_table :eggs do |t|
      t.datetime :laid_at, null: false, default: -> { 'now()' }
      t.decimal :height
      t.decimal :weight
      t.decimal :width
      t.decimal :content_health, default: 1.00
      t.decimal :shape_health, default: 1.00
      t.decimal :skin_health, default: 1.00
      t.boolean :fertilized, null: false, default: false

      t.check_constraint '0.00 <= content_health AND content_health <= 1.00', name: 'content_health_value_boundaries'
      t.check_constraint '0.00 <= shape_health AND shape_health <= 1.00', name: 'shape_health_value_boundaries'
      t.check_constraint '0.00 <= skin_health AND skin_health <= 1.00', name: 'skin_health_value_boundaries'

      t.timestamps
    end
  end
end
