class CreateGenders < ActiveRecord::Migration[7.0]
  def up
    create_table :genders do |t|
      t.string :name, null: false

      t.timestamps
    end

    execute <<-SQL.squish
      ALTER TABLE genders
      ADD CONSTRAINT valid_gender_values
      CHECK (name in ('female', 'male'));
    SQL

    %w[female male].each { |name| Gender.create(name:) }
  end

  def down
    execute <<-SQL.squish
      ALTER TABLE genders
      DROP CONSTRAINT valid_gender_values
    SQL

    drop_table :genders
  end
end
