class CreateVetVisits < ActiveRecord::Migration[7.1]
  def change
    create_table :vet_visits do |t|
      t.references :vet, null: false, foreign_key: { to_table: :personas }
      t.datetime :date, null: false
      t.references :vet_clinic, null: false, foreign_key: true
      t.boolean :at_home, null: false, default: false
      t.references :chicken, null: false, foreign_key: true
      t.text :situation_description
      t.text :doctor_verdict
      t.text :prescription
      t.boolean :injection_in_place
      t.boolean :medicine_in_place
      t.boolean :has_medicine_routine
      t.boolean :needs_second_visit
      t.integer :severity, null: false

      t.timestamps
    end
  end
end
