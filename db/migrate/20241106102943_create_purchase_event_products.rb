class CreatePurchaseEventProducts < ActiveRecord::Migration[7.1]
  def change
    create_table :purchase_event_products do |t|
      t.references :purchase_event, null: false, foreign_key: true
      t.references :product, null: false, foreign_key: true
      t.date :production_date
      t.date :expiry_date
      t.decimal :quantity
      t.references :unit, null: false, foreign_key: true
      t.decimal :paid_price
      t.decimal :original_price
      t.references :currency, null: false, foreign_key: true

      t.timestamps
    end
  end
end
