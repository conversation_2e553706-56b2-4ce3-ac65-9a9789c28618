class CreateJoinTableChickensEggs < ActiveRecord::Migration[7.0]
  def up
    create_join_table :eggs, :chickens do |t|
      t.index %i[egg_id chicken_id]
      t.index %i[chicken_id egg_id]
    end

    ActiveRecord::Base.connection.execute <<~SQL.squish
      CREATE FUNCTION prevent_roosters_from_laying_eggs() RETURNS TRIGGER AS
        $body$
          BEGIN
            IF EXISTS (SELECT * FROM chickens WHERE chickens.id = NEW.chicken_id AND chickens.gender_id = 2) THEN
              RAISE EXCEPTION 'Roosters can not lay eggs!';
            END IF;

            RETURN NEW;
          END;
        $body$
        LANGUAGE plpgsql;

      CREATE TRIGGER prevent_roosters_from_laying_eggs
        BEFORE INSERT OR UPDATE ON chickens_eggs
        FOR EACH ROW EXECUTE FUNCTION prevent_roosters_from_laying_eggs();
    SQL
  end

  def down
    ActiveRecord::Base.connection.execute <<~SQL.squish
      DROP TRIGGER IF EXISTS prevent_roosters_from_laying_eggs ON chickens_eggs;
      DROP FUNCTION IF EXISTS prevent_roosters_from_laying_eggs;
    SQL

    drop_join_table :eggs, :chickens
  end
end
