#!/bin/sh
set -e

# For development check if the gems are installed, if not
# then install them.
bundle check || bundle install;

bundle exec rails db:create
bundle exec rails db:migrate

# Remove a potentially pre-existing server.pid for Rails.
rm -f /app/tmp/pids/server.pid

# bundle exec rails db:prepare
# bundle exec rails db:migrate 2>/dev/null || bundle exec rails db:setup
# bundle exec rails db:migrate
# bundle exec rails db:seed

# Run the command
exec "$@"
