# README

This README would normally document whatever steps are necessary to get the
application up and running.

Things you may want to cover:

* Ruby version
3.2.2

* System dependencies

* Configuration

* Database creation

* Database initialization

## Development
Once the `web` service is started by a `docker compose` call, you can dive into it and boot the Rails server by running the following commad:

```
docker exec web sh
bin/dev
```

Avoid `rails server` because in that case you may miss certain Tailwind classes.

## Tailwind
Whenever you add or remove Tailwind plugins, you will nedd to rebuild the classes:

```
./_cli
rails tailwindcss:build
```

or

```
heroku run tailwindcss:build --app bergamon-family
```

* Convenient Option

  Just run `./_dev` and it will take care of everything. For it to work, make sure it is executable (chmod +x _dev).

* How to run the test suite

* Services (job queues, cache  servers, search engines, etc.)

## Database

### Create the production database

  Given that the `Heroku Postgres` addon is active, you can create a database by

  ```
  heroku run rails db:create
  ```

  The same pattern will aslo work for `db:migrate` and other commands you would normally run on the local terminal.
### Backup the production database

Yo can dowanload a backup from Hero<PERSON> by running:

```
heroku pg:backups:capture --app example-app
heroku pg:backups:download --app example-app
```

### Use the production db, locally

First, dowanload a backup which is by default called `latest.dump`, by the time of this writing. Then, put the backup in `./volumes/db`. Then, restore it from within thee local database service by running:

```
docker compose exec db sh
cd /var/lib/postgresql/data
pg_restore --verbose --clean --no-acl --no-owner -h localhost -U postgres -d app_development latest.dump
```

## Deployment

* **Initialization**

  The first time, you will need to setup Heroku. Run

  ```
  heroku create
  ```

* **Maintenamce**

  Each time the `main` branch is updated, run the following command:

  ```
  git push heroku main
  ```

  However, a simple `git push` should be just enough.

  If a password is needed for Heroku's github, run the following command:

  ```
  heroku auth:token
  ```

  Then, you most likely will need to run some migrations. In that case, run

  ```
  heroku run rails db:migrate --app bergamon-family
  ```

  You can monitor Heroku events by

  ```
  heroku logs
  ```

* **Running Tests**
As an example:

 ```
 docker compose exec web sh
 rails test test/models/egg_test.rb
 ```
