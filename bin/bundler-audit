#!/bin/bash
# Perform a security audit on project dependencies using Bundler Audit

# Ensure bundler-audit gem is installed
if ! gem list -i bundler-audit >/dev/null 2>&1; then
  echo "Installing bundler-audit gem..."
  gem install bundler-audit
fi

# Update the database of vulnerabilities
echo "Updating bundler-audit database..."
bundle audit update

# Perform the security audit
echo "Running bundler-audit..."
bundle audit check

# Check the exit code and display appropriate message
if [ $? -eq 0 ]; then
  echo "No known vulnerabilities found."
else
  echo "Potential vulnerabilities found. Please review and address them."
fi

