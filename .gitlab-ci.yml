image: ruby:3.2.2

variables:
  POSTGRES_DB: bergamon_family_test
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres
  POSTGRES_HOST_AUTH_METHOD: trust
  RAILS_ENV: test
  CI: "true"

services:
  - postgres:latest
  - selenium/standalone-chrome:latest

cache:
  paths:
    - vendor/ruby

before_script:
  - apt-get update -q && apt-get install -y nodejs postgresql-client libvips
  - gem install bundler
  - bundle install --jobs $(nproc) --path vendor/ruby
  - cp config/database.yml.ci config/database.yml || echo "Using existing database.yml"
  - bundle exec rails db:create db:schema:load

rspec:
  script:
    - bundle exec rspec
