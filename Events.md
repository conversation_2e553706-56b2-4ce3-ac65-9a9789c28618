Cook
  - On the Event of Cook, the fertility and content quality of an egg can be updated.
  - Only on the Event of Cook, the fertility of an egg can be set to true.

Request

Delivery

Give Away(date, person, location, occasion) -> Cooked or Broke or Spoiled
  - If an egg was given away to person <PERSON>, only that person can change the firtility of it through a report.
  - If an egg was given away to person <PERSON>, only that person can change the content quality of that egg through a report.
  - An administrator can also make the two changes above, by creating a Report event, according to the feedback from person <PERSON>. Upon the Report event's creation, the necessary modifications will be automatically made.
  - A GiveAway event with a successor of type RequestEvent is considered a DeliveryEvent.

(not an event) Report
  - Once an egg is given away to somebody, they can report its fertility status, and its content quality by creating a Cook event.
  - Anyone who receives an egg can give it away to someone else. In that case, they can create a GiveAway event. The receiver will receive an email to confirm. Once confirmed, the giver will gain some bonus.

Break
  Occasionally, eggs may break.

Spoilage
  After a reasonable amount of time, one can create a Spoilage event. This cannot be infered as the initial content quality.

example of an EggEvent chain
  Was cooked on Apr 13th, 2023
  Was given away on Mar 12, ’22 then cooked on Mar 13, ’22
  Was given away on Mar 12, ’22 then reported as spoiled on Mar 12, ’24

EggEvent
  egg
  date

EggRequest
  person

Delivery < EggEvent
  egg_request
  location

GiveAway < EggEvent
  person
  location

Cook < EggEvent
Break < EggEvent
Spoilage < EggEvent

rails generate scaffold DeliveryMethod type:integer 

rails generate scaffold EggRequest egg:resources user:resources expected_delivery_date



rails generate scaffold CookEggEvent \
  successor:resources \
  date:DateTime \