/*
 * This is a manifest file that'll be compiled into application.css, which will include all the files
 * listed below.
 *
 * Any CSS (and SCSS, if configured) file within this directory, lib/assets/stylesheets, or any plugin's
 * vendor/assets/stylesheets directory can be referenced here using a relative path.
 *
 * You're free to add application-wide styles to this file and they'll appear at the bottom of the
 * compiled file so the styles you add here take precedence over styles defined in any other CSS
 * files in this directory. Styles in this file should be added after the last require_* statement.
 * It is generally better to create a new file per style scope.
 *
 *= require_tree .
 *= require_self
 */

:root {
  --color-call-to-action: oklch(62.3% 0.214 259.815); /* -blue-500 */
  --color-danger: oklch(70.4% 0.191 22.216); /* -red-400 */
  --color-hover: oklch(79.5% 0.184 86.047); /* -yellow-500 */

  interpolate-size: allow-keywords;
}

.aspect-square {
  aspect-ratio: 1;
}
