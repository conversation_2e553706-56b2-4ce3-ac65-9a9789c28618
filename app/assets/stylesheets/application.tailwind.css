@tailwind base;
@tailwind components;
@tailwind utilities;

.actions {
  @apply flex items-center gap-2 justify-end;
}

@layer base {
  * {
    transition-property: background-color, border-color, outline-color, outline-width, outline-offset;
    transition-duration: 300ms;
    transition-timing-function: ease-in-out;

    @apply border-gray-900;
  }

  body {
    @apply text-gray-900;
  }

  /* Centralized heading styles */
  h1 {
    @apply font-extrabold text-xl sm:text-2xl md:text-4xl;
  }

  h2 {
    @apply font-bold text-lg sm:text-xl md:text-3xl;
  }

  h3 {
    @apply font-semibold text-lg md:text-2xl;
  }

  h4 {
    @apply font-medium text-lg md:text-xl;
  }

  h5 {
    @apply font-medium text-base md:text-lg;
  }

  h6 {
    @apply font-medium text-sm md:text-base;
  }

  a {
    @apply outline outline-4 outline-transparent;

    &:hover {
      @apply hoverable;
    }
  }

  .hoverable {
    background-color: var(--color-hover);
    outline-color: var(--color-hover);
  }
}

@layer components {
  turbo-frame {
    @apply contents;
  }

  div[data-controller] {
    @apply contents;
  }

  details::details-content {
    block-size: 0;
    transition: block-size 1s, content-visibility 1s;
    transition-behavior: allow-discrete;
    display: block;

    @apply overflow-hidden ease-in-out;
  }

  details[open]::details-content {
    block-size: auto;
  }

  details > summary:hover {
    @apply hoverable cursor-pointer;
  }

  /* Special heading variants */
  .heading-hero {
    @apply font-extrabold text-4xl md:text-5xl lg:text-6xl mb-6 text-gray-900;
  }

  .heading-section {
    @apply font-bold text-2xl md:text-3xl mb-4 text-gray-800 border-b pb-2 border-gray-200;
  }

  .heading-card {
    @apply font-semibold text-lg md:text-xl mb-2 text-gray-800;
  }

  input[type=checkbox] {
    @apply rounded;
  }

  figure select {
    @apply rounded-full drop-shadow-sm border-none p-1 ps-2;
  }

  fieldset {
    @apply border border-solid border-gray-300 rounded-md p-1 md:p-3 focus-within:border-red-600;

    &:focus-within legend {
      @apply text-red-600;
    }
  }

  legend {
    @apply capitalize px-2 text-gray-300 mb-2;
  }

  form input[type=submit], button[type=submit] {
    background-color: var(--color-call-to-action);

    @apply p-4 text-orange-50 rounded flex items-center gap-4;
  }

  nav {
    > div {
      user-select: none;
    }

    a:not([data-active=true]):hover {
      @apply hoverable;
    }

    /* nav .active { */
    [data-active=true] {
      @apply relative pointer-events-none;
    }

    [data-active=true]::after {
      content: '';
      background-color: tomato;

      @apply w-full h-2 absolute left-0 bottom-0 translate-y-full;
    }

    a {
      @apply text-gray-600 flex items-baseline gap-2;

      img {
        @apply h-full;
      }
    }
  }

  .index-page .list {
    @apply min-w-full space-y-3 max-h-[50dvh] overflow-auto;
  }

  .circular {
    aspect-ratio: 1;
    border-radius: 100%;
    overflow: hidden;
  }

  .field {
    @apply relative;

    input {
      @apply p-4;
    }

    &.framed {
      border: thin solid black;

      @apply relative px-1 md:px-4 rounded;
    }

    &.framed label,
    &:has(input:not([type=hidden], [type=checkbox], [type=radio])) label {
      @apply absolute top-0 -translate-y-1/2 left-0 md:left-2 px-2 text-xs md:text-sm text-nowrap;
    }

    &.framed label:after,
    &:has(input:not([type=hidden], [type=checkbox])) label:after {
      content: '';
      height: 0.13rem;
      z-index: -1;
      @apply absolute left-0 right-0 top-1/2 -translate-y-1/2 bg-white;
    }

    &:has(input:not([type=hidden], [type=checkbox])) input {
      @apply text-xl;
    }

    &:has(input[type=checkbox]) {
      @apply flex items-center justify-center gap-2 md:gap-4;
    }

    &:has(input[type=checkbox]).justify-start {
      justify-content: flex-start;
    }

    input:not([type=checkbox], [type=radio]) {
      @apply w-full rounded;
    }
  }

  .plus-button {
    @apply block relative w-12 h-12 circular bg-blue-600 hover:bg-blue-600 cursor-pointer;

    &::after,
    &::before {
      content: '';
      position: absolute;
      width: 50%;
      height: 15%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: white;
    }

    &::after {
      transform: translate(-50%, -50%) rotate(90deg);
    }

    &:hover {
      &::after,
      &::before {
        @apply hoverable;
      }
    }
  }

  .record-show {
    @apply relative border-2 rounded-md p-4;
  }

  .transparent-header {
    @apply w-full bg-white/75 backdrop-blur-lg drop-shadow-md z-20;
  }

  .delete-button-form {
    button[type=submit] {
      background-color: transparent;

      @apply outline outline-4;
    }

    button[type=submit]:hover {
      background-color: var(--color-danger);
      outline-color: var(--color-danger);
    }
  }
}
