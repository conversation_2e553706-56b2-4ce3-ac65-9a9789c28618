# Heading Styles Guide

This guide explains the centralized heading styles available in the application.

## Basic Heading Tags

Use the standard HTML heading tags (`h1` through `h6`) for most content. These have been styled consistently across the application:

```html
<h1>Main Page Title</h1>
<h2>Section Title</h2>
<h3>Subsection Title</h3>
<h4>Minor Section Title</h4>
<h5>Small Section Title</h5>
<h6>Smallest Section Title</h6>
```

### Styling Details

| Tag | Font Weight | Font Size (Mobile) | Font Size (Desktop) | Margin Bottom | Text Color |
|-----|-------------|-------------------|---------------------|---------------|------------|
| h1  | Extra Bold  | 1.875rem (text-3xl) | 2.25rem (text-4xl)  | 1rem (mb-4)   | Gray 900   |
| h2  | Bold        | 1.5rem (text-2xl)   | 1.875rem (text-3xl) | 0.75rem (mb-3) | Gray 800   |
| h3  | Semi-Bold   | 1.25rem (text-xl)   | 1.5rem (text-2xl)   | 0.5rem (mb-2)  | Gray 800   |
| h4  | Medium      | 1.125rem (text-lg)  | 1.25rem (text-xl)   | 0.5rem (mb-2)  | Gray 700   |
| h5  | Medium      | 1rem (text-base)    | 1.125rem (text-lg)  | 0.25rem (mb-1) | Gray 700   |
| h6  | Medium      | 0.875rem (text-sm)  | 1rem (text-base)    | 0.25rem (mb-1) | Gray 600   |

## Special Heading Variants

In addition to the standard heading tags, we've defined special heading classes for specific use cases:

### Hero Heading

Use for large, impactful page titles or hero sections:

```html
<h1 class="heading-hero">Welcome to Our Application</h1>
```

### Section Heading

Use for main section dividers with a bottom border:

```html
<h2 class="heading-section">Featured Content</h2>
```

### Card Heading

Use for card titles or compact content blocks:

```html
<h3 class="heading-card">Card Title</h3>
```

## Responsive Behavior

All heading styles are responsive:
- They use smaller font sizes on mobile devices
- They scale up appropriately for larger screens
- Font weights remain consistent across screen sizes

## Best Practices

1. **Maintain Hierarchy**: Use heading levels (h1-h6) to create a logical document structure
2. **One h1 Per Page**: Each page should generally have only one h1 element
3. **Sequential Order**: Try to use heading levels in sequential order (h1 → h2 → h3)
4. **Semantic Usage**: Choose heading levels based on content hierarchy, not appearance
5. **Special Classes**: Use the special heading classes only for their intended purposes
