<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="800"
   height="800"
   viewBox="0 0 800.00001 800.00001"
   id="svg4177"
   version="1.1"
   inkscape:version="0.91 r13725"
   sodipodi:docname="egg2.svg">
  <defs
     id="defs4179">
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter5888"
       x="-0.12810935"
       width="1.2562187"
       y="-1.0867535"
       height="3.173507">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="6.3750003"
         id="feGaussianBlur5890" />
    </filter>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient5686"
       id="radialGradient5722"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1,0,0,1.312737,0,-140.06115)"
       cx="324.69617"
       cy="447.85596"
       fx="324.69617"
       fy="447.85596"
       r="106.46809" />
    <linearGradient
       inkscape:collect="always"
       id="linearGradient5686">
      <stop
         style="stop-color:#e7ab75;stop-opacity:1;"
         offset="0"
         id="stop5688" />
      <stop
         style="stop-color:#e7ab75;stop-opacity:0;"
         offset="1"
         id="stop5690" />
    </linearGradient>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient5686"
       id="radialGradient5692"
       cx="324.69617"
       cy="447.85596"
       fx="324.69617"
       fy="447.85596"
       r="106.46809"
       gradientTransform="matrix(1,0,0,1.312737,0,-140.06115)"
       gradientUnits="userSpaceOnUse" />
  </defs>
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="0.35"
     inkscape:cx="40.714281"
     inkscape:cy="302.85718"
     inkscape:document-units="px"
     inkscape:current-layer="layer1"
     showgrid="false"
     units="px"
     showborder="false"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0"
     inkscape:window-width="1366"
     inkscape:window-height="699"
     inkscape:window-x="0"
     inkscape:window-y="24"
     inkscape:window-maximized="1" />
  <metadata
     id="metadata4182">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Layer 1"
     inkscape:groupmode="layer"
     id="layer1"
     transform="translate(62.857138,-35.219344)">
    <g
       transform="translate(-93.960234,-151.15474)"
       id="g4149">
      <rect
         rx="17.788832"
         y="186.37408"
         x="31.103096"
         height="800"
         width="800"
         id="rect4147"
         style="opacity:0.331;fill:#cccccc;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
      <g
         transform="matrix(1.7287635,0,0,1.7287635,-178.14757,-229.00613)"
         id="g5892">
        <path
           style="opacity:0.85199998;fill:#000000;fill-opacity:1;stroke:none;stroke-width:3;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1;filter:url(#filter5888)"
           d="m 412.13452,600.25734 c 0,-3.50212 -27.80751,-6.00411 -60.78698,-6.00411 -32.97948,0 -58.64225,2.50199 -58.64225,6.00411 0,3.50213 32.63318,8.07452 58.64225,8.07452 26.00906,0 60.78698,-4.57239 60.78698,-8.07452 z"
           id="path5707"
           inkscape:connector-curvature="0"
           sodipodi:nodetypes="ssszs"
           transform="matrix(1.1744279,0,0,1.2434798,-61.471869,-146.4026)" />
        <g
           id="g5698"
           transform="translate(27.723744,7.6479299)">
          <path
             sodipodi:nodetypes="ssszs"
             inkscape:connector-curvature="0"
             id="path5662"
             d="m 431.16425,468.40977 c 0,69.53416 -49.57936,119.2108 -108.38007,119.2108 -58.80071,0 -104.55611,-49.67664 -104.55611,-119.2108 0,-69.53416 58.18329,-160.31841 104.55611,-160.31841 46.37282,0 108.38007,90.78425 108.38007,160.31841 z"
             style="opacity:1;fill:#c17953;fill-opacity:1;stroke:#c17953;stroke-width:3;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
          <path
             style="opacity:0.98000004;fill:url(#radialGradient5722);fill-opacity:1;stroke:none;stroke-width:3;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
             d="m 431.16425,468.40977 c 0,69.53416 -49.57936,119.2108 -108.38007,119.2108 -58.80071,0 -104.55611,-49.67664 -104.55611,-119.2108 0,-69.53416 58.18329,-160.31841 104.55611,-160.31841 46.37282,0 108.38007,90.78425 108.38007,160.31841 z"
             id="path5684"
             inkscape:connector-curvature="0"
             sodipodi:nodetypes="ssszs" />
          <path
             sodipodi:nodetypes="ssszs"
             inkscape:connector-curvature="0"
             id="path5694"
             d="m 431.16425,468.40977 c 0,69.53416 -49.57936,119.2108 -108.38007,119.2108 -58.80071,0 -104.55611,-49.67664 -104.55611,-119.2108 0,-69.53416 58.18329,-160.31841 104.55611,-160.31841 46.37282,0 108.38007,90.78425 108.38007,160.31841 z"
             style="opacity:1;fill:url(#radialGradient5692);fill-opacity:1;stroke:none;stroke-width:3;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" />
        </g>
      </g>
    </g>
  </g>
</svg>
