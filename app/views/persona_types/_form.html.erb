<% persona_type = model %>

<%= render 'shared/model_form', model: persona_type do |form| %>
  <div class="my-5">
    <%= form.label :title %>
    <%= form.text_field :title, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:parent_id, @available_persona_types, :id, :title, { prompt: "Does this type have a parent?" }, { class: "flex-1" }) %>
    <%= render 'shared/add_button', path: new_persona_type_path if current_user&.contributor_or_admin? %>
  </div>
<% end %>
