<% if action_name == 'index' %>
  <%= with_index_page_item(purchase_event) do |item| %>
    <% item.main do %>
      <div class="grid grid-cols-[1fr_auto] grid-rows-2">
        <h1><%= purchase_event.seller.nickname %></h1>
        <h1 class="row-span-2 content-center"><%= purchase_event.currency.sign %><%= purchase_event.paid_price %></h1>
        <small><%= I18n.l(purchase_event.purchased_at, format: :abbreviated) %></small>
      </div>
    <% end %>
  <% end %>
<% else %>
  <div id="<%= dom_id purchase_event %>">
    <h1><%= purchase_event.seller.nickname %></h1>

    <p class="text-xs">
      <%= I18n.l(purchase_event.purchased_at, format: :abbreviated) %>, <%= I18n.l(purchase_event.purchased_at, format: :time) %> | <%= purchase_event.buyer.nickname %>
    </p>

    <h3 class="mt-5 mb-1">Items</h3>

    <ol>
      <% purchase_event.purchase_event_products.find_each do |purchase_event_product| %>
        <li><%= purchase_event_product.product.name %></li>
      <% end %>
    </ol>
  </div>
<% end %>
