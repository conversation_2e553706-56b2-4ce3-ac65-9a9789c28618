<% unit = model %>

<%= render 'shared/model_form', model: unit do |form| %>
  <div class="my-5">
    <%= form.label :name %>
    <%= form.text_field :name, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:unit_category_id, UnitCategory.all.order(:name), :id, :name, { prompt: "What does this unit measure?" }, { class: "flex-1" }) %>
    <%= render 'shared/add_button', path: new_unit_category_path if current_user&.contributor_or_admin? %>
  </div>

  <div class="my-5">
    <%= form.label :sign %>
    <%= form.text_field :sign, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5">
    <%= form.label :description %>
    <%= form.text_area :description, rows: 4, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>
<% end %>
