<div id="<%= dom_id egg %>" class="flex portrait:flex-col gap-4 relative">
  <script>
    function askForEggId(event) {
      event.preventDefault();

      const id = window.prompt("Enter your desired egg id:");
        if (id === null || id === '' || isNaN(id)) return;

        window.location.href = `/eggs/${id}`;
    }
  </script>

  <div class="flex-1 flex items-center justify-center relative">
    <div class="relative">
      <%= image_tag egg.avatar, class: 'max-w-full rounded-xl border' %>

      <button
        id="egg-id-link"
        class="font-bold z-10 absolute top-2 left-0 bg-white p-3 pe-6 rounded-r-full flex items-center justify-center"
        onclick="askForEggId(event)">
        <%= egg.id %>
      </button>
    </div>

    <% if egg.double_yolked  || egg.fertilized || egg.unknown_fate || egg.laid_at_time_unknown %>
      <div class="absolute top-1/2 -translate-y-1/2 right-6 md:right-2 flex flex-col gap-1">
        <% if egg.double_yolked %>
          <%= image_tag 'double-yolked-icon.svg',     class: 'h-8 w-8 p-1 overflow-hidden rounded-full bg-gray-300 invert' %>
        <% end %>
        <% if egg.fertilized %>
          <%= image_tag 'egg-fertilized-icon.svg',    class: 'h-8 w-8 p-1 overflow-hidden rounded-full bg-gray-300 invert' %>
        <% end %>
        <% if egg.unknown_fate %>
          <%= image_tag 'egg-unknown-fate-icon.svg',  class: 'h-8 w-8 p-1 overflow-hidden rounded-full bg-gray-300 invert' %>
        <% end %>
        <% if egg.laid_at_time_unknown %>
          <%= image_tag 'time-delete-icon.svg',       class: 'h-8 w-8 p-1 overflow-hidden rounded-full bg-gray-300 invert' %>
        <% end %>
      </div>
    <% end %>
  </div>

  <div class="flex-[2] xl:flex-1 flex flex-col gap-4">
    <section id="properties" class="col-span-full w-full grid grid-cols-3 grid-rows-2 gap-1 xl:gap-2">
      <div class="flex-1 grid grid-cols-3 items-center gap-1 px-2 py-4 bg-white border rounded md:rounded-lg" title="Height">
        <%= image_tag 'height-icon.svg', class: 'h-8' %>
        <div class="col-span-2 text-2xl landscape:text-xl text-left"><%= egg.height.to_i %><em class="ml-1 text-sm text-gray-500">mm</em></div>
      </div>

      <div class="flex-1 grid grid-cols-3 items-center gap-1 px-2 py-4 bg-white border rounded md:rounded-lg" title="Width">
        <%= image_tag 'width-icon.svg', class: 'h-8' %>
        <div class="col-span-2 text-2xl landscape:text-xl text-left"><%= egg.width.to_i %><em class="ml-1 text-sm text-gray-500">mm</em></div>
      </div>

      <div class="flex-1 grid grid-cols-3 items-center gap-1 px-2 py-4 bg-white border rounded md:rounded-lg" title="Weight">
        <%= image_tag 'scale-icon.svg', class: 'h-8' %>
        <div class="col-span-2 text-2xl landscape:text-xl text-left"><%= egg.weight.to_i %><em class="ml-1 text-sm text-gray-500">g</em></div>
      </div>

      <div class="flex-1 grid grid-cols-3 items-center gap-1 px-2 py-4 bg-white border rounded md:rounded-lg" title="Content health">
        <%= image_tag 'egg-content-icon.svg', class: 'h-8' %>
        <div class="col-span-2 text-2xl landscape:text-xl text-left"><%= egg.content_health %></div>
      </div>

      <div class="flex-1 grid grid-cols-3 items-center gap-1 px-2 py-4 bg-white border rounded md:rounded-lg" title="Skin health">
        <%= image_tag 'egg-skin-icon.svg', class: 'h-8' %>
        <div class="col-span-2 text-2xl landscape:text-xl text-left"><%= egg.skin_health %></div>
      </div>

      <div class="flex-1 grid grid-cols-3 items-center gap-1 px-2 py-4 bg-white border rounded md:rounded-lg" title="Shape health">
        <%= image_tag 'egg-shape-icon.svg', class: 'h-8' %>
        <div class="col-span-2 text-2xl landscape:text-xl text-left"><%= egg.shape_health %></div>
      </div>
    </section>

    <% if egg.evented? || egg.unknown_fate %>
      <section class="text-center">
        <% if egg.evented? %>
          <small class="text-sm">
            This egg has been
            <%= link_to egg.event do %>
              <% if egg.food_event_id.present? %>
                cooked
              <% elsif egg.gift_event_id.present? %>
                gifted
              <% end %>
              <% if egg.event.date.present? %>
                on <%= render_safely egg.event.date, format: :long %>.
              <% end %>
            <% end %>
          </small>
        <% elsif egg.unknown_fate %>
          <small class="text-sm">Unfortunately, we have no idea what happened to this egg.</small>
        <% end %>
      </section>
    <% end %>

    <section id="chickens-info" class="flex-1 flex flex-col items-center gap-2">
      <small class="text-sm">
        <% if egg.chickens.empty? %>
          Unfortunately, we have no idea who has laid this egg
        <% else %>
          This egg is laid by <%= 'one of these ladies' if egg.chickens.count > 1 %>
        <% end %>
        on <%= I18n.l(egg.laid_at, format: :abbreviated) %>
        <% unless egg.laid_at_time_unknown %>
          at <%= I18n.l(egg.laid_at, format: :time) %>.
        <% end %>
        on <%= I18n.l(egg.laid_at, format: :abbreviated) %>.
      </small>

      <% columns_count = egg.chickens.count.even? ? 4 : 3 %>
      <div class="grid <%= columns_count == 3 ? 'grid-cols-3' : 'grid-cols-4' %> gap-2 w-full">
        <% if egg.chickens.count < columns_count %>
          <% (columns_count - egg.chickens.count).fdiv(2).ceil.times do %>
            <div></div>
          <% end %>
        <% end %>
        <% egg.chickens.find_each do |chicken| %>
          <%= link_to(
            chicken,
            class: "w-full overflow-hidden p-1 bg-cyan-500 rounded bg-contain bg-center bg-no-repeat flex items-end text-sm whitespace-nowrap text-ellipsis",
            style: "
              background-image: linear-gradient(to bottom, transparent, white), url('#{url_for(chicken.avatar)}');
              aspect-ratio: 1;
            "
          ) do %>
            <%= chicken.name %>
          <% end %>
        <% end %>
      </div>
    </section>

    <% if @defects.present? %>
      <section id="defects">
        <span class="font-bold"><%= @defects.length > 1 ? 'Defects' : 'Defect' %>: </span><%= @defects.join(', ') %>
      </section>
    <% end %>
  </div>

  <% egg.images.each.with_index do |image, index| %>
    <%# <%= image_tag image.variant(resize_to_limit: [200, 200]).processed %>
    <%= image_tag image, loading: 'lazy' if index > 0 %>
  <% end %>

  <% if action_name != "show" %>
    <div class="col-span-3 mt-4 flex justify-end items-end gap-2">
      <%= link_to "Details...", egg, class: "rounded-lg py-3 px-5 bg-white inline-block font-medium" %>
      <%= link_to 'Edit', edit_egg_path(egg), class: "rounded-lg py-3 px-5 bg-white inline-block font-medium" %>
    </div>
  <% end %>
</div>
