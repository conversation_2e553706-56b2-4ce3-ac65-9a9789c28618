<% egg = model %>

<%= render 'shared/model_form', model: egg, locals: { data: { controller: 'egg-form' }, class: 'grid xl:grid-cols-3 gap-4 p-1 sm:p-2 md:p-4 lg:p-6 xl:p-8 rounded-lg md:outline md:outline-2xl' } do |form| %>
  <div class="col-span-full xl:col-span-1 xl:row-span-2 flex flex-col items-center bg-gray-100 rounded-lg">
    <%= form.label :images, for: 'images-file-selector' do %>
      <figure id="file-trigger" class="relative">
        <%= image_tag egg.avatar, loading: 'lazy' %>
        <figcaption class="w-max absolute bottom-0 left-1/2 -translate-x-1/2 -translate-y-1/2 text-2xl font-semibold capitalize bg-gray-200/50 backdrop-blur-lg px-2 rounded flex items-center gap-2">
          <%= image_tag 'camera-icon.svg', class: 'h-6' %>
          <span>set a photo</span>
        </figcaption>
      </figure>
    <% end %>

    <% unless egg.images.attached? %>
      <%= form.file_field :images, multiple: true, class: 'hidden', id: 'images-file-selector' %>
    <% end %>
  </div>

  <div class="col-span-full xl:col-span-2 flex flex-col">
    <div class="w-full grid auto-rows-fr sm:flex gap-2">
      <div class="field flex-1 col-span-full">
        <%= render 'shared/date_field_with_update',
          form: form,
          field_name: :laid_at,
          controller_name: 'egg-form',
          update_method: 'updateAvailableChickens',
          model: egg %>
      </div>
    </div>

    <fieldset class="grid grid-rows-1 grid-cols-2 lg:grid-cols-4 gap-2">
      <legend>Status</legend>

      <div class="field framed h-16">
        <%= form.label :fertilized %>
        <%= form.check_box :fertilized %>
        <%= image_tag 'egg-fertilized-icon.svg', class: 'h-8' %>
      </div>

      <div class="field framed h-16">
        <%= form.label :double_yolked %>
        <%= form.check_box :double_yolked %>
        <%= image_tag 'double-yolked-icon.svg', class: 'h-8' %>
      </div>

      <div class="field framed h-16">
        <%= form.label :unknown_fate %>
        <%= form.check_box :unknown_fate %>
        <%= image_tag 'egg-unknown-fate-icon.svg', class: 'h-8' %>
      </div>

      <div class="field framed h-16">
        <%= form.label :laid_at_time_unknown , 'Unknown Time' %>
        <%= form.check_box :laid_at_time_unknown %>
        <%= image_tag 'time-delete-icon.svg', class: 'h-8' %>
      </div>
    </fieldset>

    <fieldset class="grid grid-rows-2 grid-cols-3 gap-2">
      <legend>measurements</legend>

      <div class="flex-1 flex flex-col sm:grid grid-cols-3 items-center gap-1 rounded-lg bg-white" title="Height">
        <%= image_tag 'height-icon.svg', class: 'p-1' %>
        <%= form.number_field :height, step: "any", class: "col-span-2 text-2xl text-center rounded w-full" %>
      </div>

      <div class="flex-1 flex flex-col sm:grid grid-cols-3 items-center gap-1 rounded-lg bg-white" title="Width">
        <%= image_tag 'width-icon.svg', class: 'p-1' %>
        <%= form.number_field :width, step: "any", class: "col-span-2 text-2xl text-center rounded w-full" %>
      </div>

      <div class="flex-1 flex flex-col sm:grid grid-cols-3 items-center gap-1 rounded-lg bg-white" title="Weight">
        <%= image_tag 'scale-icon.svg', class: 'p-1' %>
        <%= form.number_field :weight, step: "any", class: "col-span-2 text-2xl text-center rounded w-full" %>
      </div>

      <div class="flex-1 flex flex-col sm:grid grid-cols-3 items-center gap-1 rounded-lg bg-white" title="Content health">
        <%= image_tag 'egg-content-icon.svg', class: 'p-1' %>
        <%= form.number_field :content_health, step: "any", max: "1", min: "0", class: "col-span-2 text-2xl text-center rounded w-full" %>
      </div>

      <div class="flex-1 flex flex-col sm:grid grid-cols-3 items-center gap-1 rounded-lg bg-white" title="Skin health">
        <%= image_tag 'egg-skin-icon.svg', class: 'p-1' %>
        <%= form.number_field :skin_health, step: "any", max: "1", min: "0", class: "col-span-2 text-2xl text-center rounded w-full" %>
      </div>

      <div class="flex-1 flex flex-col sm:grid grid-cols-3 items-center gap-1 rounded-lg bg-white" title="Shape health">
        <%= image_tag 'egg-shape-icon.svg', class: 'p-1' %>
        <%= form.number_field :shape_health, step: "any", max: "1", min: "0", class: "col-span-2 text-2xl text-center rounded w-full" %>
      </div>
    </fieldset>

    <fieldset class="col-span-full border border-solid border-gray-300 rounded-md p-3 focus-within:border-red-600">
      <legend class="capitalize px-2">laid by</legend>

      <ul class="flex flex-wrap gap-x-8 gap-y-2 space-y-4">
        <%= turbo_frame_tag :available_chickens do %>
          <%= collection_check_boxes(:egg, :chicken_ids, @available_chickens, :id, :name, {}, { multiple: true }) do |choice| %>
            <li class="flex items-center gap-4">
              <%= choice.check_box class: 'p-4' %>
              <%= choice.label class: 'text-2xl' %>
            </li>
          <% end %>
        <% end %>
      </ul>
    </fieldset>

    <fieldset class="grid grid-rows-1 grid-cols-4 gap-2 capitalize">
      <legend>defects</legend>

      <div class="field framed h-16">
        <%= form.label :nader_defect, 'nader' %>
        <%= form.check_box :nader_defect %>
      </div>

      <div class="field framed h-16">
        <%= form.label :juan_jose_defect, 'juan jose' %>
        <%= form.check_box :juan_jose_defect %>
      </div>

      <div class="field framed h-16">
        <%= form.label :mahlagha_defect, 'mahlagha' %>
        <%= form.check_box :mahlagha_defect %>
      </div>

      <div class="field framed h-16">
        <%= form.label :shahbagha_defect, 'shahbagha' %>
        <%= form.check_box :shahbagha_defect %>
      </div>
    </fieldset>
  </div>

  <%# egg.images.each do |image| %>
    <%# <%= image_tag image.variant(resize_to_limit: [200, 200]).processed %>
    <%#= image_tag image %>
  <%# end %>
<% end %>
