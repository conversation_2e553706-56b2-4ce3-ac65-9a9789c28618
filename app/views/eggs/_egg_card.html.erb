<div class="relative h-fit rounded overflow-hidden" style="aspect-ratio: 1">
  <%= image_tag egg.avatar, class: 'w-full absolute top-1/2 -translate-y-1/2', onclick: "#{onclick}.call(this, #{egg.id})", loading: 'lazy' %>

  <div class="absolute top-0 h-full w-full bg-gradient-to-t from-sky-900 mix-blend-overlay pointer-events-none"></div>

  <button
    id="egg-id-link"
    class="font-semibold text-sm z-10 absolute top-2 left-0 bg-white p-2 rounded-r-full drop-shadow-md"
    onclick="askForEggId(event)">
    <%= egg.id %>
  </button>

  <% if egg.evented? %>
    <span
      class="font-semibold text-sm z-10 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 text-white drop-shadow-sm text-center">
      <% if egg.unknown_fate %>
        Unknown
      <% elsif egg.event.is_a? FoodEvent %>
        Food
      <% elsif egg.event.is_a? GiftEvent %>
        Gift
      <% else %>
        <%= egg.event.class %>
      <% end %>
    </span>
  <% end %>

  <div class="font-semibold absolute bottom-4 md:bottom-2 inset-x-6 md:inset-x-2 bg-gray-200/25 backdrop-blur-sm px-2 py-1 rounded-md flex gap-4 drop-shadow-md">
    <div class="flex-1 flex items-center justify-between text-sm md:text-xs font-light h-4">
      <span><%= I18n.l(egg.laid_at, format: :abbreviated) %></span>
      <div class="flex gap-1 align-center justify-center">
        <%= image_tag 'double-yolked-icon.svg', class: 'h-4', title: 'double-yolked' if egg.double_yolked %>
        <%= image_tag 'egg-fertilized-icon.svg', class: 'h-4', title: 'fertilized' if egg.fertilized %>
        <%= image_tag 'egg-unknown-fate-icon.svg', class: 'h-4', title: 'unknown fate' if egg.unknown_fate %>
        <%= image_tag 'time-delete-icon.svg', class: 'h-4', title: 'unknown time' if egg.laid_at_time_unknown %>
      </div>
      <% unless egg.laid_at_time_unknown %>
        <span><%= I18n.l(egg.laid_at, format: :time) %></span>
      <% end %>
    </div>
  </div>

  <div class="absolute top-4 md:top-2 right-6 md:right-2 h-8">
    <% egg.chickens.each_with_index do |chicken, index| %>
    <% shadow_color = chicken.color.sub(/\)$/, ', 0.5)').sub(/^rgb/, 'rgba') %>
      <%= render 'shared/chicken_avatar',
                  chicken:,
                  class: 'w-14 md:w-10',
                  as_background: chicken.has_own_avatar?,
                  style: {
                    transform: "translateX(#{(egg.chickens.count - index) * -0.5}rem) translateY(-#{index * 100}%);",
                    'box-shadow': "3px 3px 6px rgba(0,0,0,0.5) inset, -2px -2px 4px rgba(255,255,255,0.25) inset, 2px 2px 2px rgba(0,0,0,0.25), 3px 3px 9px #{shadow_color}",
                  } %>
    <% end %>
  </div>
</div>
