<style>
  form {
    margin: auto;
  }
</style>

<%= form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>
  <fieldset class="flex flex-col gap-4 p-4" data-controller="password-visibility">
    <legend>Log in</legend>

    <div class="field">
      <%= f.label :email %>
      <%= f.email_field :email, autofocus: true, autocomplete: "email" %>
    </div>

    <div class="field">
      <%= f.label :password %>
      <%= f.password_field :password, autocomplete: "current-password", data: { password_visibility_target: "passwordField" } %>
    </div>

    <div class="field justify-start">
      <input type="checkbox" id="show_password" data-password-visibility-target="toggleCheckbox" data-action="change->password-visibility#toggleVisibility" class="rounded">
      <label for="show_password" class="text-sm text-gray-600">Show password (10 seconds)</label>
    </div>

    <% if devise_mapping.rememberable? %>
      <div class="field justify-start">
        <%= f.check_box :remember_me %>
        <%= f.label :remember_me %>
      </div>
    <% end %>

    <div class="actions">
      <%= f.submit "Log in" %>
    </div>
  </fieldset>
<% end %>

<%#= render "devise/shared/links" %>
