<style>
  form {
    margin: auto;
  }
</style>

<%= form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>
  <fieldset class="flex flex-col gap-8 p-4">
    <legend>Log in</legend>

    <div class="field">
      <%= f.label :email %>
      <%= f.email_field :email, autofocus: true, autocomplete: "email" %>
    </div>

    <div class="field">
      <%= f.label :password %>
      <%= f.password_field :password, autocomplete: "current-password" %>
    </div>

    <% if devise_mapping.rememberable? %>
      <div class="field">
        <%= f.check_box :remember_me %>
        <%= f.label :remember_me %>
      </div>
    <% end %>

    <div class="actions">
      <%= f.submit "Log in" %>
    </div>
  </fieldset>
<% end %>

<%#= render "devise/shared/links" %>
