<style>
  form {
    margin: auto;
  }
</style>

<%= form_for(resource, as: resource_name, url: registration_path(resource_name), class: 'w-full') do |f| %>
  <%= render "devise/shared/error_messages", resource: resource %>

  <fieldset class="flex flex-col gap-8">
    <legend>Sign Up</legend>

    <div class="field">
      <%= f.label :email %>
      <%= f.email_field :email, autofocus: true, autocomplete: "email", class: 'w-full rounded' %>
    </div>

    <div class="field">
      <%= f.label :password %>
      <% if @minimum_password_length %>
      <small class="absolute bottom-0 translate-y-full opacity-50">(<%= @minimum_password_length %> characters minimum)</small>
      <% end %>
      <%= f.password_field :password, autocomplete: "new-password" %>
    </div>

    <div class="field">
      <%= f.label :password_confirmation %>
      <%= f.password_field :password_confirmation, autocomplete: "new-password" %>
    </div>

    <div class="actions">
      <%= f.submit "Sign up" %>
    </div>
  </fieldset>
<% end %>

<%#= render "devise/shared/links" %>
