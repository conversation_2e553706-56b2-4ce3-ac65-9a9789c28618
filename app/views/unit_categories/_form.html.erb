<% @unit_category = model %>

<%= render 'shared/model_form', model: unit_category do |form| %>
  <div class="my-5">
    <%= form.label :name %>
    <%= form.text_field :name, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5">
    <%= form.label :description %>
    <%= form.text_area :description, rows: 4, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:parent_id, UnitCategory.where.not(id: @unit_category&.id), :id, :name, { prompt: "Does it have a parent category?" }, { class: "flex-1" }) %>
  </div>
<% end %>
