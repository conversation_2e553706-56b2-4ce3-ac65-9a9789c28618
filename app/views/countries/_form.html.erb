<% country = model %>

<%= render 'shared/model_form', model: country do |form| %>
  <div class="my-5">
    <%= form.label :name %>
    <%= form.text_field :name, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5">
    <%= form.label :code %>
    <%= form.text_field :code, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:continent_id, Continent.all.order(:name), :id, :name, { prompt: "In which continent is it located?" }, { class: "flex-1" }) %>
    <%= render 'shared/add_button', path: new_continent_path if current_user&.contributor_or_admin? %>
  </div>
<% end %>
