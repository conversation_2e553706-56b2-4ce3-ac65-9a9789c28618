<% if action_name == 'index' %>
  <%
    title = food_event.food_type.name
    subtitle = food_event.date.present? ? I18n.l(food_event.date, format: :abbreviated) : nil
  %>

  <%= with_index_page_item(food_event, { title:, subtitle: }) do |item| %>
    <% item.after_link do %>
      <% if food_event.eggs.any? %>
        <%= food_event.eggs.count %>
        <%= image_tag 'eggs-icon.svg', class: 'h-4 inline', alt: 'eggs', title: 'eggs' %>
      <% end %>
    <% end %>
  <% end %>
<% else %>
  <div id="<%= dom_id food_event %>" class="space-y-6">
    <h1><%= link_to food_event.food_type.name, food_event.food_type %></h1>
    <h5><%= food_event.date.present? ? I18n.l(food_event.date, format: :long) : 'Unknown date' %></h5>

    <%= render 'shared/event_egg_contribution', event: food_event %>
  </div>
<% end %>
