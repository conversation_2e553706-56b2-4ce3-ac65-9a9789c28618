<%
  chicken = broodiness_period.chicken

  start_day_string = broodiness_period.start_day.present? ? I18n.l(broodiness_period.start_day, format: :abbreviated) : 'Unknown'
  end_day_string = broodiness_period.end_day.present? ? I18n.l(broodiness_period.end_day, format: :abbreviated) : 'Unknown'
%>

<% if action_name == 'index' %>
  <%
    subtitle = [start_day_string, end_day_string].join(' - ')
  %>

  <%= with_index_page_item(broodiness_period, { title: broodiness_period.chicken.name, subtitle: }) do |item| %>
    <% if broodiness_period.days.present? %>
      <% item.after_link do %>
        <div class="flex flex-col gap-1 items-end">
          <h2 class="font-bold text-xl"><%= broodiness_period.days %></h2>
          <div class="flex gap-1 items-baseline">
            <small><%= broodiness_period.days == 1 ? 'day' : 'days' %></small>
          </div>
        </div>
      <% end %>
    <% end %>
  <% end %>
<% else %>
  <div class="flex gap-4 items-center">
    <%= image_tag 'broodiness-period-icon.svg', class: 'w-16 lg:w-24' %>

    <div class="flex-1 space-y-8">
      <h1><%= start_day_string %> — <%= end_day_string %></h1>

      <%= link_to chicken, class: "row-span-2 flex gap-4 items-center w-fit rounded-md" do %>
        <%= render 'shared/chicken_avatar', chicken:, class: 'w-8 md:w-12' %>
        <h2><%= chicken.name %></h2>
      <% end %>
    </div>

    <div class="flex flex-col gap-1 items-end">
      <h2><%= broodiness_period.days %></h2>
      <h3 class="text-gray-400"><%= broodiness_period.days == 1 ? 'day' : 'days' %></h3>
    </div>
  </div>
<% end %>
