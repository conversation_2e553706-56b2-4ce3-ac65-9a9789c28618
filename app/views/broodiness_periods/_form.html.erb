<% broodiness_period = model %>

<%= render 'shared/model_form', model: broodiness_period, locals: { data: { controller: "broodiness-period-record-form" } } do |form| %>
  <div class="my-5 flex">
    <%= turbo_frame_tag :available_chickens do %>
      <%= form.collection_select(
          :chicken_id,
          @available_chickens,
          :id,
          :name,
          { prompt: 'Choose a chicken' },
          { class: 'flex-1' }
        ) %>
    <% end %>
  </div>

  <%= render 'shared/date_field_with_update',
    form: form,
    field_name: :start_day,
    controller_name: 'broodiness-period-record-form',
    update_method: 'updateAvailableChickens',
    label_text: 'Started on',
    set_default: false,
    model: broodiness_period %>

  <%= render 'shared/date_field_with_update',
    form: form,
    field_name: :end_day,
    controller_name: 'broodiness-period-record-form',
    update_method: 'updateAvailableChickens',
    label_text: 'Finished on',
    set_default: false,
    model: broodiness_period %>
<% end %>
