<%
  severity_color = case vet_visit.severity
                     when 'safe'      then 'bg-green-500'
                     when 'low'       then 'bg-green-500'
                     when 'moderate'  then 'bg-yellow-500'
                     when 'high'      then 'bg-orange-500'
                     when 'critical'  then 'bg-red-500'
                   end
%>

<% if action_name == 'index' %>
  <%= with_index_page_item(vet_visit) do |item| %>
    <% item.main do %>
      <div class="grid grid-cols-[auto_1fr_auto] gap-2 items-center">
        <div class="w-2 rounded h-full <%= severity_color %>"></div>

        <div>
          <div class="font-bold"><%= vet_visit.chicken.name %></div>
          <div class="text-sm flex gap-1">
            <%= image_tag 'home-icon.svg', class: 'h-4' if vet_visit.at_home %>
            Dr. <%= vet_visit.vet.nickname %>
          </div>
          <small> <%= I18n.l(vet_visit.date, format: :abbreviated) %></small>
        </div>

        <div class="flex-1 flex gap-2 justify-end">
          <%= image_tag 'injection-icon.svg', class: 'h-8' if vet_visit.injection_in_place %>
          <%= image_tag 'medicine-icon.svg', class: 'h-8' if vet_visit.medicine_in_place || vet_visit.has_medicine_routine %>
          <%= image_tag 'prescription-icon.svg', class: 'h-8' if vet_visit.prescription.present? %>
          <%= image_tag 'timer-icon.svg', class: 'h-8' if vet_visit.needs_second_visit %>
        </div>
      </div>
    <% end %>

    <% item.after_link do %>
      <% if vet_visit.previous_visit.present? %>
        <%= link_to(image_tag('go-down-icon.svg', class: 'h-8'), vet_visit_path(vet_visit.previous_visit)) %>
      <% else %>
        <%= image_tag('go-down-icon.svg', class: 'h-8 opacity-0') %>
      <% end %>
    <% end %>
  <% end %>
<% else %>
  <div id="<%= dom_id vet_visit %>" class="grid grid-cols-2 <%= 'border rounded-lg p-6' if action_name == 'index' %>">
    <% if action_name != "show" %>
      <% if false %>
        <%= link_to "Show this visit", vet_visit, class: "rounded-lg py-3 px-5 bg-gray-100 inline-block font-medium" %>
      <% end %>

      <% if current_user&.contributor_or_admin? %>
        <%= render 'shared/edit_button', target: edit_vet_visit_path(vet_visit) %>
      <% end %>
    <% end %>

    <strong class="block font-medium">Vet visit ID:</strong>
    <%= link_to vet_visit.id, vet_visit_path(vet_visit) %>

    <strong class="block font-medium">Vet:</strong>
    <%= link_to vet_visit.vet.nickname, persona_path(vet_visit.vet) %>

    <strong class="block font-medium">Date:</strong>
    <%= I18n.l(vet_visit.date, format: :abbreviated) %>

    <strong class="block font-medium">Vet clinic:</strong>
    <%= link_to vet_visit.vet_clinic.name, vet_clinic_path(vet_visit.vet_clinic) %>

    <strong class="block font-medium">Chicken:</strong>
    <%= link_to vet_visit.chicken.name, chicken_path(vet_visit.chicken) %>

    <strong class="block font-medium">At home:</strong>
    <%= vet_visit.at_home %>

    <% if vet_visit.situation_description.present? %>
      <strong class="block font-medium">Situation description:</strong>
      <%= vet_visit.situation_description %>
    <% end %>

    <% if vet_visit.doctor_verdict.present? %>
      <strong class="block font-medium">Doctor verdict:</strong>
      <%= vet_visit.doctor_verdict %>
    <% end %>

    <% if vet_visit.prescription.present? %>
      <strong class="block font-medium">Prescription:</strong>
      <%= vet_visit.prescription %>
    <% end %>

    <strong class="block font-medium">Injection in place:</strong>
    <%= vet_visit.injection_in_place ? '✔️' : '⎯' %>

    <strong class="block font-medium">Medicine in place:</strong>
    <%= vet_visit.medicine_in_place ? '✔️' : '⎯' %>

    <strong class="block font-medium">Has medicine routine:</strong>
    <%= vet_visit.has_medicine_routine ? '✔️' : '⎯' %>

    <strong class="block font-medium">Needs second visit:</strong>
    <%= vet_visit.needs_second_visit ? '✔️' : '⎯' %>

    <strong class="block font-medium">Severity:</strong>
    <%= vet_visit.severity %>

    <strong class="block font-medium">Previous visit:</strong>
    <% if vet_visit.previous_visit.present? %>
      <%= link_to "#{I18n.l(vet_visit.previous_visit.date, format: :abbreviated)} by #{vet_visit.previous_visit.vet.nickname}", vet_visit_path(vet_visit.previous_visit) %>
    <% else %>
      ⎯
    <% end %>
  </div>
<% end %>
