<% chicken = model %>

<%= render 'shared/model_form', model: chicken, id: 'egg-form', locals: { class: 'grid auto-rows-auto md:grid-cols-2 gap-2 md:gap-12 lg:gap-4' } do |form| %>
  <%= form.label :images, for: 'images-file-selector', class:'row-span-2 flex' do %>
    <fieldset id="file-trigger" class="relative flex items-center" role="button">
      <legend class="flex items-center gap-1">
        <%= image_tag 'camera-icon.svg', class: 'h-4 opacity-25' %>
        Set a photo
      </legend>
      <%= turbo_frame_tag :avatar do %>
        <%= image_tag chicken.avatar,
          width: '100%',
          class: 'bg-yellow-300 px-16 py-8 rounded-lg',
          style: 'background-image: linear-gradient(to bottom, transparent, rgb(234 179 8))',
          loading: 'lazy' %>
      <% end %>
    </fieldset>

    <% unless chicken.has_own_avatar? %>
      <%= form.file_field :images, multiple: true, class: 'hidden', id: 'images-file-selector' %>
    <% end %>
  <% end %>

  <fieldset id="identity" class="flex flex-col gap-8" data-controller="chicken-form">
    <legend>identity</legend>

    <section id="name" class="field">
      <%= form.label :name %>
      <%= form.text_field :name %>
    </section>

    <section id="gender" class="field flex gap-4">
      <%= form.button 'Update Gender',
        formaction: chicken.persisted? ? edit_chicken_path(chicken) : new_chicken_path,
        formmethod: :get,
        style: 'display: none',
        data: {
          chicken_form_target: 'updateAvatar',
          turbo_frame: :avatar
        } %>

      <%= form.collection_radio_buttons :gender_id, Gender.all, :id, :name do |b| %>
        <div class="inline-flex items-center gap-2 capitalize" data-action="change->chicken-form#updateAvatar">
          <%= b.radio_button %>
          <%= b.label %>
        </div>
      <% end %>
    </section>

    <section id="color" class="field">
      <%= form.label :color %>
      <%= form.text_field :color, type: 'color', class: "h-16" %>
    </section>
  </fieldset>

  <fieldset id="life-data" class="flex flex-col gap-8">
    <legend>dates</legend>

    <div class="field">
      <%= form.label :birth_day %>
      <%= form.datetime_field :birth_day %>
    </div>
    <div class="field">
      <%= form.label :death_day %>
      <%= form.datetime_field :death_day %>
    </div>
    <div class="field">
      <%= form.label :join_day %>
      <%= form.datetime_field :join_day %>
    </div>
    <div class="field">
      <%= form.label :leave_day %>
      <%= form.datetime_field :leave_day %>
    </div>
  </fieldset>
<% end %>
