<% document = model %>

<%= render 'shared/model_form', model: document do |form| %>
  <div class="my-5">
    <%= form.label :title %>
    <%= form.text_field :title, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5">
    <%= form.label :description %>
    <%= form.text_area :description, rows: 4, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5">
    <%= form.label :issued_on %>
    <%= form.date_field :issued_on, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5">
    <%= form.label :expires_on %>
    <%= form.date_field :expires_on, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:document_type_id, @available_document_types, :id, :title, { prompt: "What type of document is it?" }, { class: "flex-1" }) %>
    <%= render 'shared/add_button', path: new_document_type_path if current_user&.contributor_or_admin? %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:issuer_id, @available_personas, :id, :nickname, { prompt: "Who issued this?" }, { class: "flex-1" }) %>
    <%= render 'shared/add_button', path: new_persona_path if current_user&.contributor_or_admin? %>
  </div>

  <div class="my-5 flex gap-2 relative">
    <fieldset class="relative">
      <legend>Over whom is this issued?</legend>

      <ul class="flex flex-wrap gap-x-8 gap-y-2 space-y-4">
        <%= collection_check_boxes(:document, :holder_ids, @available_personas, :id, :nickname, { multiple: true, class: "flex-1" }) do |choice| %>
          <li class="flex items-center gap-4">
            <%= choice.check_box class: 'p-4' %>
            <%= choice.label class: 'text-2xl' %>
          </li>
        <% end %>
      </ul>
    </fieldset>

    <div class="w-12 h-12">
      <%= render 'shared/add_button', path: new_persona_path if current_user&.contributor_or_admin? %>
    </div>
  </div>

  <div class="field">
    <%= form.label :images %>
    <%= form.file_field :images, multiple: true %>
  </div>
<% end %>
