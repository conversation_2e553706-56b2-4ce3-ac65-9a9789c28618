<% if action_name == 'index' %>
  <%
    title = document.title
    subtitle = I18n.l(document.issued_on, format: :abbreviated) if document.issued_on.present?
  %>
  <%= with_index_page_item(document, { title:, subtitle: }) do |item| %>
    <% item.after_link do %>
      <div class="flex flex-col gap-1 text-end">
        <small><%= document.holders.pluck(:nickname).sort.join(', ') %></small>
        <% if document.document_type.present? %>
          <small><%= document.document_type.title %></small>
        <% end %>
      </div>
    <% end %>
  <% end %>
<% else %>
  <div id="<%= dom_id document %>">
    <p class="my-5">
      <strong class="block font-medium mb-1">Title:</strong>
      <%= document.title %>
    </p>

    <% if document.description.present? %>
      <p class="my-5">
        <strong class="block font-medium mb-1">Description:</strong>
        <%= document.description %>
      </p>
    <% end %>

    <% if document.issued_on.present? %>
      <p class="my-5">
        <strong class="block font-medium mb-1">Issued on:</strong>
        <%= document.issued_on %>
      </p>
    <% end %>

    <% if document.expires_on.present? %>
      <p class="my-5">
        <strong class="block font-medium mb-1">Expires on:</strong>
        <%= document.expires_on %>
      </p>
    <% end %>

    <% if document.document_type.present? %>
      <p class="my-5">
        <strong class="block font-medium mb-1">Document type:</strong>
        <%= document.document_type.title %>
      </p>
    <% end %>

    <% if document.issuer.present? %>
      <p class="my-5">
        <strong class="block font-medium mb-1">Issuer:</strong>
        <%= document.issuer.nickname %>
      </p>
    <% end %>

    <% if document.holders.any? %>
      <p class="my-5">
        <strong class="block font-medium mb-1">Held by:</strong>
        <%= document.holders.pluck(:nickname).sort.join(', ') %>
      </p>
    <% end %>

    <% if document.images.any? %>
      <div class="grid grid-cols-2 lg:grid-cols-4">
        <% document.images.each do |image| %>
          <%= link_to image_path(image) do %>
            <%= image_tag image.file, alt: image.label.presence || "Document Image" %>
          <% end %>
        <% end %>
      </div>
    <% end %>

    <% if action_name != "show" %>
      <%= link_to "Show this document", document, class: "rounded-lg py-3 px-5 bg-gray-100 inline-block font-medium" %>
      <%= link_to "Edit this document", edit_document_path(document), class: "rounded-lg py-3 ml-2 px-5 bg-gray-100 inline-block font-medium" %>
      <hr class="mt-6">
    <% end %>
  </div>
<% end %>
