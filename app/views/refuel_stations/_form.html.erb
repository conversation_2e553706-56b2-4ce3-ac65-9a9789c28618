<% refuel_station = model %>

<%= render 'shared/model_form', model: refuel_station do |form| %>
  <div class="my-5 flex gap-2">
    <%= form.collection_select(:brand_id, Persona.order(:nickname), :id, :nickname, { prompt: "Which brand?" }, { class: "flex-1" }) %>
    <%= render 'shared/add_button', path: new_persona_path if current_user&.contributor_or_admin? %>
  </div>

  <%= render 'shared/city_selector', form: %>

  <div class="my-5">
    <%= form.label :nickname %>
    <%= form.text_field :nickname, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5">
    <%= form.label :address %>
    <%= form.text_field :address, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5">
    <%= form.label :latitude %>
    <%= form.number_field :latitude, step: "any", class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5">
    <%= form.label :longitude %>
    <%= form.number_field :longitude, step: "any", class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>
<% end %>
