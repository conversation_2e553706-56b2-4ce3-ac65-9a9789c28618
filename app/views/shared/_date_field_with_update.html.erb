<%
  # Required parameters
  form = local_assigns.fetch(:form)
  field_name = local_assigns.fetch(:field_name)
  controller_name = local_assigns.fetch(:controller_name)
  update_method = local_assigns.fetch(:update_method)

  # Optional parameters
  label_text = local_assigns.fetch(:label_text, field_name.to_s.humanize)
  model = local_assigns.fetch(:model, form.object)
  set_default = local_assigns.fetch(:set_default, nil)
  update_path = local_assigns.fetch(:update_path, nil)
  turbo_frame = local_assigns.fetch(:turbo_frame, "available_#{update_method.to_s.sub('updateAvailable', '').downcase}".to_sym)

  # Auto-generate update path if not provided
  update_path ||= model.persisted? ? url_for([:edit, model]) : url_for([:new, model.class.name.underscore.to_sym])
%>

<div class="my-5">
  <%= form.label field_name, label_text %>
  <%= form.datetime_field field_name,
    class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full",
    data: {
      action: "change->#{controller_name}##{update_method}",
      default_date: set_default == false ? nil : 'now'
    } %>
</div>

<%= form.button 'Update',
  formaction: update_path,
  formmethod: :get,
  style: 'display: none',
  data: {
    "#{controller_name.underscore}_target".to_sym => update_method,
    turbo_frame:
  } %>
