<%#
  Renders a standardized index page layout

  Local variables:
  - title: The page title (required)
  - collection_name: The name of the collection in plural form (required)
  - new_path: Path to the new resource form (optional)
  - icon_path: Path to the icon image (optional)
  - statistics: Optional statistics text to display (optional)
  - collection_class: Additional CSS classes for the collection container (optional)
  - yield: If true, the content block will be rendered instead of the collection (optional but necessary to render the block)
%>

<div class="w-full space-y-8 index-page">
  <%= render 'shared/notice' %>

  <div class="flex items-center gap-8 p-2 mb-8 sticky top-10 z-10 bg-white/75 backdrop-blur-sm">
    <% if local_assigns[:icon_path].present? %>
      <%= image_tag icon_path, class: 'h-8 md:h-12' %>
    <% end %>
    <h1 class="flex-1"><%= title %></h1>
    <%= render 'shared/add_button', path: new_path if local_assigns[:new_path].present? && current_user&.contributor_or_admin? %>
  </div>

  <% if local_assigns[:statistics].present? %>
    <div class="landscape:contents portrait:hidden portrait:md:contents">
      <%= render 'shared/chart', **local_assigns[:statistics] %>
    </div>
  <% end %>

  <% css_classes = [] %>
  <% css_classes << collection_class if local_assigns[:collection_class].present? %>

  <% if local_assigns[:yield] %>
    <%= yield %>
  <% else %>
    <ul
      id="<%= collection_name %>"
      class="min-w-full space-y-2 px-1 <%= css_classes.join(' ') %>"
    >
      <%= render instance_variable_get("@#{collection_name}") %>
    </ul>
  <% end %>

  <%= will_paginate instance_variable_get("@#{collection_name}"), class: 'text-center' %>
</div>
