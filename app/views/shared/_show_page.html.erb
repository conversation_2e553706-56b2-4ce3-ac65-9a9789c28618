<%
  avoid_default_rendering ||= false
  crud_at_end ||= false
  yield_before ||= false

  delete_path ||= polymorphic_path(model)
  edit_path   ||= polymorphic_path([:edit, model])
  list_path   ||= polymorphic_path([model.class])
  new_path    ||= polymorphic_path([:new, model])
%>

<div class="space-y-2">
  <%= render 'shared/notice' %>

  <%= yield if yield_before %>

  <% unless avoid_default_rendering == true %>
    <div class="record-show">
      <%= render model %>

      <div class="w-12 h-12 absolute right-2 top-0 -translate-y-1/2">
        <%= render 'shared/add_button', path: new_path if current_user&.contributor_or_admin? %>
      </div>
    </div>
  <% end %>

  <%= render 'shared/crud_buttons', { edit_path:, delete_path:, list_path: } unless crud_at_end %>

  <%= yield unless yield_before %>

  <%= render 'shared/crud_buttons', { edit_path:, delete_path:, list_path: } if crud_at_end %>
</div>
