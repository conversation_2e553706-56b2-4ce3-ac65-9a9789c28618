<% if event.eggs.any? %>
  <div class="space-y-4">
    <div class="text-xs text-gray-500 flex gap-1 items-baseline">
      <%= image_tag 'eggs-icon.svg', class: 'h-4 inline' %>
      <%= pluralize event.eggs.count, 'Egg' %>
    </div>

    <div class="grid grid-cols-5 gap-2 lg:grid-cols-8">
      <% event.eggs.find_each do |egg| %>
        <%= render 'shared/egg_link', egg: %>
      <% end %>
    </div>
  </div>
<% end %>

<% if @chicken_contributions.any? %>
  <div class="space-y-4">
    <div class="text-xs text-gray-500 flex gap-1 items-baseline">
      <%= image_tag 'gift-chickens-icon.svg', class: 'h-4 inline' %>
      Contributions
    </div>

    <div class="grid grid-cols-[3fr_auto_auto_auto_2fr] gap-2 items-center">
      <% @chicken_contributions.each do |chicken_id, data| %>
        <div class="relative h-1/2 rounded-sm overflow-hidden bg-slate-200">
          <div class="h-full rounded-sm bg-slate-500" style="width: <%= data[:normalized_contribution_rate] * 100 %>%"></div>
        </div>
        <span class="whitespace-nowrap text-right text-gray-500"><%= data[:contribution].round(1) %></span>
        <span class="whitespace-nowrap text-left text-gray-500"><%= data[:contribution] > 1 ? 'eggs' : 'egg' %></span>
        <%= render 'shared/chicken_avatar', chicken: data[:chicken], class: 'w-8 md:w-8' %>
        <span
          class="font-semibold whitespace-nowrap w-full overflow-hidden text-ellipsis text-start"
          style="color: <%= data[:chicken].color %>">
          <%= data[:chicken].name %>
        </span>
      <% end %>
    </div>
  </div>
<% end %>