# Index Page Partial

This partial provides a standardized way to create index pages across the application.

## Usage

```erb
<%= render 'shared/index_page',
  title: "Your Resource Title",
  collection_name: "your_resources",
  new_path: new_your_resource_path,
  icon_path: "your-icon.svg" do %>
  <%= render @your_resources %>
<% end %>
```

## Parameters

- `title`: The page title (required)
- `collection_name`: The name of the collection in plural form (required)
- `new_path`: Path to the new resource form (optional)
- `icon_path`: Path to the icon image (optional)
- `statistics`: Optional statistics text to display (optional)
- `collection_class`: Additional CSS classes for the collection container (optional)

## Examples

### Basic Usage

```erb
<%= render 'shared/index_page',
  title: "Documents",
  collection_name: "documents",
  new_path: new_document_path,
  icon_path: "documents-icon.svg" do %>
  <%= render @documents %>
<% end %>
```

### With Statistics

```erb
<%
  statistics_prompt = "#{@weight_records.count} samples over #{time_span_prompt(WeightRecord.first.date.midday, WeightRecord.last.date.midday)}."
  statistics = {
                 average_coordinates: @chicken_weight_averages,
                 caption: statistics_prompt,
                 categorized_coordinates: @chicken_weight_records_categorized_coordinates,
                 chart_id: 'chicken-weight-chart',
                 timespan_selector_id: 'chicken-weight-chart-time-span-selector',
                 vertical_axis_title: 'Weight (kg)',
               }
%>

<%= render 'shared/index_page',
  title: "Weight records",
  collection_name: "cities",
  new_path: new_weight_record_path,
  icon_path: "scale-icon.svg",
  statistics: statistics do %>
  <%= render @weight_records %>
<% end %>
```

### With Collection Class

```erb
<%= render 'shared/index_page',
  title: "Personas",
  collection_name: 'personas',
  new_path: new_persona_path,
  icon_path: "persona-icon.svg",
  collection_class: 'space-y-2' do %>
  <%= render @personas %>
<% end %>
```

## Notes

- This partial automatically handles the display of notices
- The "New" button is only shown to contributors or admins
- The collection is rendered inside a div with the provided collection_name as its ID
- The partial maintains the standard styling and layout used across the application
