<%
  id = rand.to_s.split('.').last
  icon = local_assigns[:icon]
%>

<div id="filters-box-<%= id %>" class="flex flex-col gap-8 p-2 md:p-0 rounded-lg outline outline-2">
  <button id="filters-toggle-<%= id %>"
    class="flex items-center gap-2 font-thin cursor-pointer transition-all rounded p-4 hover:bg-gray-100"
    onclick="toggleFiltersBox()">
    <%= image_tag icon, class: 'w-4 h-4' if icon.present? %>
    <span class="text-xs font-semibold"><%= title %></span>
    <span class="flex-1 flex justify-end">
      <%= image_tag 'plus-icon.svg', id: "plus-icon-#{id}", class: 'w-4 h-4 hidden' %>
      <%= image_tag 'minus-icon.svg', id: "minus-icon-#{id}", class: 'w-4 h-4' %>
    </span>
  </button>

  <div id="filters-<%= id %>" class="flex flex-col gap-2 transition-all px-2">
    <%= yield %>
  </div>
</div>

<script>
  function toggleFiltersBox() {
    const filters = document.querySelector('#filters-<%= id %>');
    const plusIcon = document.querySelector('#plus-icon-<%= id %>');
    const minusIcon = document.querySelector('#minus-icon-<%= id %>');
    const filtersToggle = document.querySelector('#filters-toggle-<%= id %>');

    plusIcon.classList.toggle('hidden');
    minusIcon.classList.toggle('hidden');
    filters.classList.toggle('flex');
    filters.classList.toggle('hidden');
    filtersToggle.classList.toggle('font-thin');
  }
</script>