<% if action_name == 'index' %>
  <%
    title = render_safely(sell_event.date)
    subtitle = sell_event.persona&.nickname
  %>
  <%= with_index_page_item(sell_event, { title:, subtitle: }) do |item| %>
    <% item.after_link do %>
      <div class="flex flex-col gap-1 items-end">
        <h2 class="font-bold text-xl"><%= sell_event.price %>€</h2>
        <div class="flex gap-1 items-baseline">
          <% if sell_event.eggs.any? %>
            <%= sell_event.eggs.count %>
            <%= image_tag 'eggs-icon.svg', class: 'h-4 inline', alt: 'eggs', title: 'eggs' %>
          <% end %>
        </div>
      </div>
    <% end %>
  <% end %>
<% else %>
  <div id="<%= dom_id sell_event %>" class="space-y-8 p-2">
    <div class="space-y-4">
      <h1><%= render_safely sell_event.persona %></h1>
      <h3>€<%= render_safely sell_event.price %></h3>
      <h5><%= render_safely sell_event.date %></h5>
    </div>

    <p>
      Sold on <%= render_safely sell_event.date %>
      to <%= render_safely sell_event.persona %>
      for </strong>€<%= render_safely sell_event.price %>
    </p>

    <%= render 'shared/event_egg_contribution', event: sell_event %>
  </div>
<% end %>
