<% klass = local_assigns[:class] || '' %>

<div class="<%= klass %> flex justify-end gap-2 lg:gap-4 leading-10" style="<%= style if defined?(style) %>">
  <% if current_user.present? %>
    <%= link_to "Logout", destroy_user_session_path, method: :delete, data: { turbo_method: :delete } %>
  <% else %>
    <%= link_to "Login", new_user_session_path, data: { active: current_page?('/users/sign_in') } %>
    <%= link_to "Sign Up", new_user_registration_path, data: { active: current_page?('/users/sign_up') } %>
  <% end %>
</div>
