<!DOCTYPE html>
<html>
  <head>
    <title>Bergamon Family</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "tailwind", "inter-font", "data-turbo-track": "reload" %>

    <%= tag.link rel: "manifest", href: "/site.webmanifest" %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/luxon"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon"></script>

  <script>
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isFirefox = /Firefox/i.test(navigator.userAgent) && !/Android/i.test(navigator.userAgent);

    const isMacOS = /^((?!windows|linux|android).)*mac/i.test(navigator.platform);
    const isAndroid = /Android/i.test(navigator.userAgent);
  </script>

  <body class="relative">
    <%= render 'layouts/wait_dialog' %>

    <main class="container mx-auto py-20 px-1 md:px-5">
      <%= yield %>
    </main>

    <%= render 'layouts/main_nav' %>

    <% if flash[:alert] %>
      <div class="alert alert-danger fixed top-0 left-0 right-0 p-16 flex items-center bg-white drop-shadow-2xl z-30" role="alert">
        <div class="flex-1 flex items-center justify-center">
          <%= flash[:alert] %>
        </div>
        <button type="button"
          onclick="document.querySelector('.alert').style.display='none'"
          class="w-10 h-10 text-xl bg-gray-900 text-gray-50 rounded float-right">
          &times;
        </button>
      </div>
    <% end %>
  </body>

  <script>
    function pleaseWait() {
      document.querySelector('#please-wait').showModal();
    }

    function navigateTo(url) {
      pleaseWait();

      window.location.href = url.toString();
    }
  </script>
</html>
