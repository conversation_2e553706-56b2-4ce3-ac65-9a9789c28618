<% persona = model %>

<%= render 'shared/model_form', model: persona do |form| %>
  <div class="my-5">
    <%= form.label :nickname %>
    <%= form.text_field :nickname, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:alias_for_id, @available_users, :id, :email, { prompt: "Is this an alias for an existing user? Whom, then?" }, { class: "flex-1" }) %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:persona_type_id, @available_persona_types, :id, :title, { prompt: "Is it a legal entity or a human or what?" }, { class: "flex-1" }) %>
    <%= render 'shared/add_button', path: new_persona_type_path if current_user&.contributor_or_admin? %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:relationship_id, @available_relationships, :id, :title, { prompt: "What type of relationship do you have?" }, { class: "flex-1" }) %>
    <%= render 'shared/add_button', path: new_relationship_path if current_user&.contributor_or_admin? %>
  </div>
<% end %>
