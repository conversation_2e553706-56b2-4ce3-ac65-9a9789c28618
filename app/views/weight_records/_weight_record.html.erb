<% if action_name == 'index' %>
  <%
    title = weight_record.entity.name
    subtitle = I18n.l(weight_record.date, format: :abbreviated)
    previous_record = weight_record.entity.weight_records.where('date < ?', weight_record.date).order(date: :desc).first
    delta = previous_record.present? ? weight_record.value - previous_record.value : nil
    delta_sign =  case true
                  when delta.blank?
                    nil
                  when delta == 0
                    'value-maintain-icon.svg'
                  when delta < 0
                    'chart-down-icon.svg'
                  when delta > 0
                    'chart-up-icon.svg'
                  end
    dangerous_delta = delta.present? && (delta / previous_record.value).abs >= 0.1

    random_id = rand.to_s.split('.').last
  %>
  <%= with_index_page_item(weight_record, { title:, subtitle: }) do |item| %>
    <% item.after_link do %>
      <h2 class="heading-card flex items-center gap-1">
        <div class="flex items-baseline gap-1">
          <span><%= weight_record.display_value.round(1) %></span>
          <span class="text-gray-500"><%= weight_record.display_unit %></span>
        </div>
        <div class="w-6 h-6 danger-icon-<%= random_id %>"></div>
      </h2>
    <% end %>
  <% end %>
<% else %>
  <div class="space-y-8 p-2">
    <h1>
      <span class="text-7xl"><%= @weight_record.display_value > 1000 ? format('%.3f', @weight_record.display_value) : @weight_record.display_value %></span>
      <span class="text-gray-500 font-normal"><%= @weight_record.display_unit %></span>
    </h1>

    <h4><span class="font-black"><%= I18n.l(@weight_record.date, format: :abbreviated) %></span></h4>

    <%= link_to @weight_record.entity, class: "row-span-2 flex gap-4 items-center w-fit rounded-md" do %>
      <%= render 'shared/chicken_avatar', chicken: @weight_record.entity, class: 'w-8 md:w-12' %>
      <h2><%= @weight_record.entity.name %></h2>
    <% end %>
  </div>
<% end %>

<style>
  <% if delta_sign.present? %>
    .danger-icon-<%= random_id %> {
      background-color: <%= dangerous_delta ? 'tomato' : 'olive' %> !important; /* Your desired icon color */
      -webkit-mask: url('<%= asset_path(delta_sign) %>') no-repeat center;
      -webkit-mask-size: contain;
      mask: url('<%= asset_path(delta_sign) %>') no-repeat center;
      mask-size: contain;
    }
  <% end %>
</style>
