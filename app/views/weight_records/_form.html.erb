<% weight_record = model %>

<%= render 'shared/model_form', model: weight_record, locals: { data: { controller: "weight-record-form" } } do |form| %>
  <div class="my-5 flex">
    <%= turbo_frame_tag :available_chickens do %>
      <%= form.collection_select(
          :entity_id,
          @available_chickens,
          :id,
          :name,
          { prompt: 'Choose a chicken' },
          { class: 'flex-1' }
        ) %>
    <% end %>
  </div>

  <%= render 'shared/date_field_with_update',
    form: form,
    field_name: :date,
    controller_name: 'weight-record-form',
    update_method: 'updateAvailableChickens',
    label_text: 'Sampled on',
    model: weight_record %>

  <div class="my-5">
    <%= form.label :value, 'Weight in grams' %>
    <%= form.number_field :value, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>
<% end %>
