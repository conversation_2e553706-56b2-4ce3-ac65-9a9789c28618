<% product = model %>

<%= render 'shared/model_form', model: product do |form| %>
  <div class="my-5 flex gap-2">
    <%= form.collection_select(:product_category_id, ProductCategory.all.order(:name), :id, :name, { prompt: "To which category does it belong?" }, { class: "flex-1" }) %>
    <%= render 'shared/add_button', path: new_product_category_path if current_user&.contributor_or_admin? %>
  </div>

  <div class="my-5 flex gap-2">
    <%= form.collection_select(:supplier_id, Persona.all.order(:nickname), :id, :nickname, { prompt: "Who has produced it?" }, { class: "flex-1" }) %>
    <%= render 'shared/add_button', path: new_persona_path if current_user&.contributor_or_admin? %>
  </div>

  <div class="my-5">
    <%= form.label :name %>
    <%= form.text_field :name, class: "block shadow rounded-md border border-gray-200 outline-none px-3 py-2 mt-2 w-full" %>
  </div>
<% end %>
