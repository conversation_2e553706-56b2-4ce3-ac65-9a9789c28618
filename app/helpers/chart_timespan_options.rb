module ChartTimespanOptions
  ENTIRE_HISTORY = 'ENTIRE_HISTORY'.freeze
  LAST_10_DAYS   = 'LAST_10_DAYS'.freeze
  LAST_MONTH     = 'LAST_MONTH'.freeze
  LAST_3_MONTHS  = 'LAST_3_MONTHS'.freeze
  LAST_6_MONTHS  = 'LAST_6_MONTHS'.freeze
  LAST_12_MONTHS = 'LAST_12_MONTHS'.freeze

  def self.all
    {
      ENTIRE_HISTORY => 'Entire history',
      LAST_10_DAYS => 'Last 10 days',
      LAST_MONTH => 'Last month',
      LAST_3_MONTHS => 'Last 3 months',
      LAST_6_MONTHS => 'Last 6 months',
      LAST_12_MONTHS => 'Last 12 months'
    }
  end
end
