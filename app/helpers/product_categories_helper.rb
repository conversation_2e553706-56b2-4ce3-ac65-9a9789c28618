module ProductCategoriesHelper
  def render_category_tree(tree)
    return if tree.blank?

    content_tag :div, class: 'list-disc ml-6' do
      tree.map { |node|
        content_tag(node[:children].empty? ? :desscription : :details) do
          concat content_tag(
            :summary,
            link_to(node[:name], edit_product_category_path(node[:id]),
                    class: 'font-medium text-blue-600 hover:underline')
          )
          concat render_category_tree(node[:children])
        end
      }.join.html_safe
    end
  end
end
