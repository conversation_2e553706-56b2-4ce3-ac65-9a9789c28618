module ApplicationHelper
  SECONDS_IN_A_DAY = 86_400

  def spacer(size = 2)
    content_tag(:div, nil, class: "py-#{size}")
  end

  def time_span_prompt(start, finish = 1.second.ago)
    start  = 1.second.ago if start.blank?
    finish = 2.seconds.ago if finish.blank?

    return nil if start > finish

    days = (finish - start) / SECONDS_IN_A_DAY

    if days < 90
      "#{days.to_i} days"
    elsif days < 2 * 365
      "#{(days / 30.0).to_i} months"
    else
      "#{(days / 365.0).to_i} years"
    end
  end

  def render_safely(item, **options)
    return 'Unknown' if item.blank?

    case item.class.to_s
    when 'Date', 'DateTime', 'ActiveSupport::TimeWithZone'
      I18n.l(item, format: options[:format] || :abbreviated)
    when 'Persona'
      link_to(item.nickname, item)
    else
      item.to_s
    end
  end

  class IndexPageItem
    attr_reader :record, :view_context, :options

    def initialize(view_context, record, options = {})
      @view_context = view_context
      @record = record
      @options = options
      @blocks = {}
    end

    def main(&block)
      @blocks[:main] = block
    end

    def after_link(&block)
      @blocks[:after_link] = block
    end

    def render
      tag = options[:tag] || :li

      view_context.content_tag(tag, id: view_context.dom_id(@record), class: 'flex gap-2 items-center', 'data-test-index-page-item': @record.id) do
        parts = []

        if view_context.current_user&.contributor_or_admin?
          parts << view_context.render(partial: 'shared/edit_button',
                                       locals: { target: view_context.edit_polymorphic_path(@record) })
        end

        parts << view_context.link_to(view_context.polymorphic_path(@record),
                                      class: 'flex-1 w-full px-2 py-1 rounded') do
          content = []
          content << view_context.content_tag(:h4, options[:title], class: 'font-bold') if options[:title].present?
          content << view_context.content_tag(:h6, options[:subtitle]) if options[:subtitle].present?
          content << view_context.content_tag(:h6, options[:subtitle2]) if options[:subtitle2].present?
          content << view_context.capture(&@blocks[:main]) if @blocks[:main]
          view_context.safe_join(content)
        end

        parts << view_context.capture(&@blocks[:after_link]) if @blocks[:after_link]

        view_context.safe_join(parts)
      end
    end
  end

  def with_index_page_item(record, options = {})
    item = IndexPageItem.new(self, record, options)
    yield item if block_given?
    item.render
  end
end
