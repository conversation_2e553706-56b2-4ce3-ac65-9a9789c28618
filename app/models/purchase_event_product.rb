class PurchaseEventProduct < ApplicationRecord
  belongs_to :purchase_event
  belongs_to :product
  belongs_to :unit
  belongs_to :currency

  validates :original_price, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true
  validates :paid_price, numericality: { greater_than_or_equal_to: 0 }, presence: true
  validates :quantity, numericality: { greater_than: 0 }, presence: true

  validate :production_date_before_expiry_date

  private

  def production_date_before_expiry_date
    return if production_date.nil? || expiry_date.nil?

    return if production_date <= expiry_date

    errors.add(:production_date, 'must be before or equal to the expiry date')
  end
end
