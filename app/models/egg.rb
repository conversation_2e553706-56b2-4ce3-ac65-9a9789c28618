class Egg < ApplicationRecord
  attribute :laid_at, :datetime

  belongs_to :food_event, optional: true
  belongs_to :gift_event, optional: true
  belongs_to :sell_event, optional: true

  has_and_belongs_to_many :chickens
  has_many_attached :images, dependent: :destroy

  scope :available, -> { where(food_event_id: nil, gift_event_id: nil, unknown_fate: false) }
  scope :cooked, -> { where.not(food_event_id: nil) }
  scope :gifted, -> { where.not(gift_event_id: nil) }
  scope :identifiable, -> { joins(:chickens).distinct }
  scope :identified, -> { joins(:chickens).group('eggs.id').having('COUNT(chickens.id) = 1') }
  scope :weighted, -> { where.not(weight: nil) }

  validates :laid_at, presence: true

  validate :laid_by_some_available_chicken?
  validate :unknown_fate_must_be_false_if_not_available

  def avatar
    images.first || ActionController::Base.helpers.asset_path('Realistic-egg.svg')
  end

  def siblings
    Egg.where(laid_at: laid_at.all_day).where.not(id:).order(:id)
  end

  def event = evented? ? (food_event || gift_event || sell_event) : nil
  def evented? = food_event_id.present? || gift_event_id.present?

  def data_missing?
    return true if weight.blank?
    return true if width.blank?
    return true if height.blank?
    return true if images.count.zero?
    return true if chickens.empty?

    false
  end

  private

  def laid_by_some_available_chicken?
    return false if chickens.all? { |chicken| chicken.can_lay_eggs_at? laid_at }

    # return if Chickens.female.order(:id).pluck(:id) == Chicken.laying.order(:id).pluck(:id)

    errors.add(:chickens, :invalid)
  end

  def unknown_fate_must_be_false_if_not_available
    return unless unknown_fate && evented?

    errors.add(:unknown_fate, 'cannot be true if associated with a food event or gift event')
  end
end
