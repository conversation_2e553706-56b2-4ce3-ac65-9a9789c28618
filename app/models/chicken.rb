class Chicken < ApplicationRecord
  belongs_to :gender

  has_and_belongs_to_many :eggs
  has_many :broodiness_periods, dependent: :destroy
  has_many :vet_visits, dependent: :destroy
  has_many :weight_records, as: :entity, dependent: :destroy
  has_many_attached :images, dependent: :destroy

  validates :name, presence: true, uniqueness: true

  scope :female, -> { where(gender_id: 1) }
  scope :male,   -> { where(gender_id: 2) }

  scope :weighted, -> { joins(:weight_records).distinct }

  scope :born,    ->(date = Time.zone.today) { where('birth_day IS NOT NULL AND birth_day <= ?', date) }
  scope :joined,  ->(date = Time.zone.today) { where('join_day <= ?', date) }
  scope :alive,   lambda { |date = Time.zone.today|
    born(date).or(joined(date)).where('death_day IS NULL OR death_day >= ?', date)
  }
  scope :dead,    ->(date = Time.zone.today) { where('death_day <= ?', date) }
  scope :present, ->(date = Time.zone.today) { born(date).or(joined(date)).where(death_day: nil, leave_day: nil) }

  scope :laying,  ->(at: Time.zone.today) { female.present(at) }

  def can_lay_eggs_at?(date)
    return false if gender == Gender.male

    raise 'No date specified' if date.blank?

    return false if not_born?(date)
    return false if not_joined?(date)
    return false if dead?(date)
    return false if left?(date)

    true
  end

  def female? = gender == Gender.female
  def male? = !female?

  def self.male_avatar
    ActionController::Base.helpers.asset_path 'rooster-silhouette.png'
  end

  def self.female_avatar
    ActionController::Base.helpers.asset_path 'hen-silhouette.png'
  end

  def has_own_avatar? = images.attached?

  def avatar
    return images.first if has_own_avatar?

    male? ? Chicken.male_avatar : Chicken.female_avatar
  end

  def identifiable_eggs = Egg.identifiable.joins(:chickens).where(chickens: { id: })
  def identified_eggs = Egg.identified.joins(:chickens).where(chickens: { id: })

  def age
    return unless birth_day.present? && leave_day.blank?

    days = ((death_day || Time.zone.today).to_date - birth_day.to_date).to_i

    years = days / 365
    months = (days % 365) / 30
    days_left = (days % 365) % 30

    "#{years} years, #{months} months, #{days_left} days"
  end

  private

  def not_born?(date)
    birth_day.present? && date < birth_day
  end

  def not_joined?(date)
    join_day.present? && date < join_day
  end

  def dead?(date)
    death_day.present? && date > death_day
  end

  def left?(date)
    leave_day.present? && date > leave_day
  end
end
