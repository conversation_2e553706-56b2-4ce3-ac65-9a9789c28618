class VetVisit < ApplicationRecord
  enum severity: {
    safe: 0,
    low: 1,
    moderate: 2,
    high: 3,
    critical: 4
  }

  belongs_to :chicken
  belongs_to :previous_visit, class_name: 'VetVisit', optional: true
  belongs_to :vet_clinic, optional: true
  belongs_to :vet, class_name: '<PERSON><PERSON>'

  has_many :vet_visits, foreign_key: :previous_visit_id, dependent: :destroy

  validates :severity, :date, presence: true

  validate :either_at_home_or_in_clicnic
  validate :previous_visit_cannot_be_after_current_visit

  private

  def either_at_home_or_in_clicnic
    return if at_home && vet_clinic.blank?
    return if !at_home && vet_clinic.present?

    errors.add(:vet_clinic, :invalid)
  end

  def previous_visit_cannot_be_after_current_visit
    return if date.blank? || previous_visit&.date.blank?

    errors.add(:previous_visit, :invalid) if previous_visit.date >= date
  end
end
