class PurchaseEvent < ApplicationRecord
  belongs_to :buyer, class_name: '<PERSON><PERSON>'
  belongs_to :seller, class_name: '<PERSON><PERSON>'

  has_many :purchase_event_products, dependent: :destroy

  accepts_nested_attributes_for :purchase_event_products, allow_destroy: true

  validates :purchased_at, presence: true

  def paid_price
    purchase_event_products.sum(&:paid_price)
  end

  def currency
    purchase_event_products.first&.currency
  end
end
