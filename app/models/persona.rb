class Persona < ApplicationRecord
  belongs_to :alias_for, optional: true, class_name: 'User'
  belongs_to :persona_type, optional: true
  belongs_to :relationship, optional: true

  has_many :purchases, class_name: 'PurchaseEvent', foreign_key: 'buyer_id', dependent: :nullify
  has_many :sells, class_name: 'PurchaseEvent', foreign_key: 'seller_id', dependent: :nullify

  has_and_belongs_to_many :documents, inverse_of: :holders

  validates :persona_type, absence: { message: 'cannot be set if this is an alias for a user' },
                           if: -> { alias_for.present? }
  validates :relationship, absence: { message: 'cannot be set if this is an alias for a user' },
                           if: -> { alias_for.present? }
  validates :relationship, presence: { message: 'must be present when the type is not specified.' },
                           if: -> { persona_type.nil? && alias_for.nil? }
end
