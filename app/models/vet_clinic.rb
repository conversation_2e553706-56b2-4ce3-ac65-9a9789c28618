class VetClinic < ApplicationRecord
  # Strip leading and trailing spaces to avoid uniqueness issues with whitespace variations
  before_validation :strip_whitespace

  validates :name, :address, presence: true
  validates :name,
            uniqueness: { scope: %i[address city_id], message: 'with this address already exists in this city' }

  belongs_to :city

  has_many :vet_visits, dependent: :destroy
  has_many :chickens, through: :vet_visits

  private

  def strip_whitespace
    self.name = name&.strip.presence
    self.address = address&.strip.presence
  end
end
