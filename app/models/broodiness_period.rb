class BroodinessPeriod < ApplicationRecord
  belongs_to :chicken

  validate :chicken_is_female, if: -> { chicken.present? }
  validate :start_day_before_end_day

  def days
    return nil unless start_day.present?

    duration = (end_day || Time.zone.today).beginning_of_day - start_day.beginning_of_day
    (duration / 86_400).to_i
  end

  private

  def chicken_is_female
    errors.add(:chicken, 'must be female') unless chicken&.female?
  end

  def start_day_before_end_day
    return if start_day.nil? || end_day.nil?
    return if start_day <= end_day

    errors.add(:start_day, 'must be before the end day')
  end
end
