import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="eggs"
export default class extends Controller {
  static targets = ['updateGallery', 'updateCalendar', 'updateStatistics'];

  restrictToActiveMonthInput = () => document.querySelector('#eggs-index-form input[name="restrict_to_active_month"]');
  chickenIdInput             = () => document.querySelector('#eggs-index-form input[name="chicken_id"]');
  yearInput                  = () => document.querySelector('#eggs-index-form input[name="year"]');
  monthInput                 = () => document.querySelector('#eggs-index-form input[name="month"]');
  dayInput                   = () => document.querySelector('#eggs-index-form input[name="day"]');
  statisticsInput            = () => document.querySelector('#eggs-index-form input[name="statistics"]');
  activeYearPrompt           = () => document.querySelector('#active-year-prompt');
  activeMonthPrompt          = () => document.querySelector('#active-month-prompt');

  connect() {
    // Listen for turbo frame loads to redraw charts
    document.addEventListener('turbo:frame-load', this.handleFrameLoad.bind(this));
  }

  disconnect() {
    document.removeEventListener('turbo:frame-load', this.handleFrameLoad.bind(this));
  }

  handleFrameLoad(event) {
    if (event.target.id === 'statistics') {
      // Small delay to ensure DOM is fully updated
      setTimeout(() => this.redrawChart(), 100);
    }
  }

  redrawChart() {
    const statisticsValue = this.statisticsInput().value;

    const timespan = (selector) => document.querySelector(selector)?.value || 'LAST_10_DAYS';

    switch (statisticsValue) {
      case 'weight':
        drawChartEggsWeightChart?.('eggsWeightChart', timespan('#eggs-weight-chart-time-span-selector'));
      case 'width':
        drawChartEggsWidthChart?.('eggsWidthChart', timespan('#eggs-width-chart-time-span-selector'));
      default:
        break;
    }
  }

  restrictToActiveMonth({ currentTarget }) {
    // Update the UI
    const url = new URL(window.location.href);
    if (currentTarget.checked) {
      url.searchParams.set('restrict_to_active_month', true);
    } else {
      url.searchParams.delete('restrict_to_active_month');
    }

    window.history.pushState(null, '', url.toString());

    // Send the request
    this.restrictToActiveMonthInput().checked = currentTarget.checked;
    this.updateGallery();
  }

  restrictToChicken({ currentTarget }) {
    this.chickenIdInput().value = currentTarget.value || undefined;

    // Send the request
    this.updateCalendar();
    this.updateGallery();
  }

  setDay({ currentTarget }) {
    const { day } = currentTarget.dataset;
    this.dayInput().value = day;

    // Update the interface
    const url = new URL(window.location.href);
    url.searchParams.set('day', day);
    window.history.pushState(null, '', url.toString());

    // Send the request
    this.updateCalendar();
    this.updateGallery();
  }

  setDate({ currentTarget }) {
    const date = new Date(currentTarget.value || currentTarget.dataset.date);

    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    // Update the hidden form
    this.yearInput().value = year;
    this.monthInput().value = month;
    this.dayInput().value = day;

    // Update the interface
    const url = new URL(window.location.href);
    url.searchParams.set('year', year);
    url.searchParams.set('month', month);
    url.searchParams.set('day', day);
    window.history.pushState(null, '', url.toString());

    // Send the request
    this.updateCalendar();
    this.updateGallery();
  }

  setMonth({ currentTarget }) {
    const date  = new Date(currentTarget.value);
    const year  = date.getFullYear();
    const month = date.getMonth() + 1;

    // Update the hidden form
    this.yearInput().value = year;
    this.monthInput().value = month;
    this.dayInput().value = null;

    try {
      this.activeMonthPrompt().innerText = date.toLocaleString('default', { month: 'short' });
      this.activeYearPrompt().innerText = year;
    } catch (exc) {
      debugger;
    }

    // if (!this.restrictToActiveMonthInput().checked) return;

    // Update the interface
    const url = new URL(window.location.href);
    url.searchParams.set('year', year);
    url.searchParams.set('month', month);
    window.history.pushState(null, '', url.toString());

    // Send the request
    this.updateCalendar();
    this.updateGallery();
  }

  updateCalendar() {
    this.updateCalendarTarget.click();
  }

  updateGallery() {
    this.updateGalleryTarget.click();
  }

  updateStatistics() {
    this.updateStatisticsTarget.click();
  }

  toggleChart({ currentTarget }) {
    const chartType = currentTarget.dataset.chartType;

    // Update the URL
    const url = new URL(window.location.href);
    if (url.searchParams.get('statistics') === chartType) {
      this.statisticsInput().value = '';
      url.searchParams.delete('statistics');
    } else {
      this.statisticsInput().value = chartType;
      url.searchParams.set('statistics', chartType);
    }
    window.history.pushState(null, '', url.toString());

    // Update the form and send request
    this.updateStatistics();
  }
}
