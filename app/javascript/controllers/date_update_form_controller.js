import { Controller } from "@hotwired/stimulus"

// Base controller for forms with date fields that trigger updates
// Connects to data-controller="date-update-form"
export default class extends Controller {
  // Common method to trigger update by clicking the hidden button
  updateTarget(targetName) {
    this[`${targetName}Target`]?.click();
  }

  // Generic update method that can be called with different target names
  updateAvailableItems(targetName = 'updateAvailableItems') {
    this.updateTarget(targetName);
  }
}
