import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="password-visibility"
export default class extends Controller {
  static targets = ["passwordField", "toggleCheckbox"]

  connect() {
    this.timeoutId = null
  }

  disconnect() {
    this.clearTimeout()
  }

  toggleVisibility() {
    const isChecked = this.toggleCheckboxTarget.checked

    if (isChecked) {
      // Show password
      this.passwordFieldTarget.type = "text"

      // Set timeout to hide password after 10 seconds
      this.timeoutId = setTimeout(() => {
        this.hidePassword()
      }, 10000) // 10 seconds
    } else {
      // Hide password immediately
      this.hidePassword()
    }
  }

  hidePassword() {
    this.passwordFieldTarget.type = "password"
    this.toggleCheckboxTarget.checked = false
    this.clearTimeout()
  }

  clearTimeout() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId)
      this.timeoutId = null
    }
  }
}
