class FoodEventsController < ApplicationController
  before_action :set_food_types, only: %i[new edit]
  before_action :set_food_event, only: %i[show edit update destroy]
  before_action :set_available_cookers, only: %i[new edit]
  before_action :set_available_eggs, only: %i[new edit]

  # GET /food_events or /food_events.json
  def index
    @food_events = authorize FoodEvent.order(date: :desc)
      .then(&apply_pagination(per_page: 12))
  end

  # GET /food_events/1 or /food_events/1.json
  def show
    set_chickens_contribution_to_food_event(authorize(@food_event))

    @food_event
  end

  # GET /food_events/new
  def new
    @food_event = authorize FoodEvent.new food_event_params
  end

  # GET /food_events/1/edit
  def edit
    authorize @food_event
  end

  # POST /food_events or /food_events.json
  def create
    @food_event = authorize FoodEvent.new(food_event_params)

    respond_to do |format|
      if @food_event.save
        format.html { redirect_to food_event_url(@food_event), notice: 'Food event was successfully created.' }
        format.json { render :show, status: :created, location: @food_event }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @food_event.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /food_events/1 or /food_events/1.json
  def update
    authorize @food_event

    respond_to do |format|
      if @food_event.update(food_event_params)
        format.html { redirect_to food_event_url(@food_event), notice: 'Food event was successfully updated.' }
        format.json { render :show, status: :ok, location: @food_event }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @food_event.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /food_events/1 or /food_events/1.json
  def destroy
    (authorize @food_event).destroy!

    respond_to do |format|
      format.html { redirect_to food_events_url, notice: 'Food event was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_available_cookers
    @available_cookers = User.order(:email)
  end

  def set_available_eggs
    date = begin
      DateTime.parse food_event_params[:date]
    rescue StandardError
      @food_event&.date || 1.second.ago
    end

    not_evented_eggs = Egg.available
      .where('laid_at < ?', date)
      .order(id: :desc)

    already_included_eggs = Egg.where(food_event_id: @food_event&.id || -1)

    @available_eggs = not_evented_eggs.or(already_included_eggs)
  end

  def set_food_event
    @food_event = FoodEvent.find(params[:id])
  end

  def set_food_types
    @food_types = FoodType.order(:name)
  end

  # Only allow a list of trusted parameters through.
  def food_event_params
    params.fetch(:food_event, {}).permit(
      :food_type_id,
      :date,
      :cooker_id,
      egg_ids: []
    )
  end

  def set_chickens_contribution_to_food_event(food_event)
    chicken_contributions = Hash.new do |hash, key|
      hash[key] = { chicken: nil, contribution: 0.0, contribution_rate: 0.0, normalized_contribution_rate: 0.0 }
    end

    total_eggs = food_event.eggs.count
    max_contribution_rate = 0.0

    food_event.eggs.each do |egg|
      # Get all chickens associated with the egg
      chickens = egg.chickens
      next if chickens.empty?

      # Calculate the contribution (1/n) for this particular egg
      contribution = 1.0 / chickens.count

      # Add or update each chicken's contribution probability
      chickens.find_each do |chicken|
        chicken_contributions[chicken.id][:chicken] = chicken
        chicken_contributions[chicken.id][:contribution] += contribution
        chicken_contributions[chicken.id][:contribution_rate] =
          chicken_contributions[chicken.id][:contribution] / total_eggs
        if chicken_contributions[chicken.id][:contribution_rate] > max_contribution_rate
          max_contribution_rate = chicken_contributions[chicken.id][:contribution_rate]
        end
      end
    end

    if max_contribution_rate.positive?
      chicken_contributions.each do |_, contribution_data|
        contribution_data[:normalized_contribution_rate] =
          contribution_data[:contribution_rate] / max_contribution_rate
      end
    end

    @chicken_contributions = chicken_contributions
  end
end
