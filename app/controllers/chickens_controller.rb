class ChickensController < ApplicationController
  before_action :set_chicken, only: %i[show edit update destroy]

  # GET /chickens or /chickens.json
  def index
    @chickens = Chicken.order(:name)
      .then(&apply_pagination(per_page: 60))
  end

  # GET /chickens/1 or /chickens/1.json
  def show
    @chicken_weight_categorized_coordinates = {
      @chicken.name => {
        color: @chicken.color,
        coordinates: @chicken.weight_records
          .order(:date)
          .pluck(:date, :value)
          .map { |date, weight| { x: date.to_time.iso8601, y: weight / 1000.0 } }
      }
    }

    @chicken_egg_weight_categorized_coordinates = {
      @chicken.name => {
        color: @chicken.color,
        coordinates: @chicken.eggs.identified.weighted
          .order(:laid_at)
          .pluck(:laid_at, :weight)
          .map { |date, weight| { x: date.to_time.iso8601, y: weight } }
      }
    }
  end

  # GET /chickens/new
  def new
    @chicken = Chicken.new.tap { |chicken| chicken.assign_attributes chicken_params }

    authorize @chicken
  end

  # GET /chickens/1/edit
  def edit
    authorize @chicken
  end

  # POST /chickens or /chickens.json
  def create
    @chicken = Chicken.new(chicken_params)

    authorize @chicken

    respond_to do |format|
      if @chicken.save
        format.html { redirect_to chicken_url(@chicken), notice: I18n.t('flash.notice.item_created', item: 'Chicken') }
        format.json { render :show, status: :created, location: @chicken }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @chicken.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /chickens/1 or /chickens/1.json
  def update
    authorize @chicken

    respond_to do |format|
      if @chicken.update(chicken_params)
        format.html { redirect_to chicken_url(@chicken), notice: I18n.t('flash.notice.item_updated', item: 'Chicken') }
        format.json { render :show, status: :ok, location: @chicken }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @chicken.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /chickens/1 or /chickens/1.json
  def destroy
    authorize @chicken

    @chicken.destroy

    respond_to do |format|
      format.html { redirect_to chickens_url, notice: I18n.t('flash.notice.item_destroyed', item: 'Chiken') }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_chicken
    @chicken = Chicken.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def chicken_params
    params.require(:chicken).permit(
      :birth_day,
      :color,
      :death_day,
      :gender_id,
      :join_day,
      :leave_day,
      :name,
      images: []
    )
  rescue ActionController::ParameterMissing
    {}
  end
end
