class PurchaseEventsController < ApplicationController
  before_action :set_purchase_event, only: %i[show edit update destroy]
  before_action :set_available_buyers, only: %i[new create edit update]
  before_action :set_available_products, only: %i[new create edit update]
  before_action :set_available_sellers, only: %i[new create edit update]

  # GET /purchase_events or /purchase_events.json
  def index
    @purchase_events = PurchaseEvent.all
      .then(&apply_pagination(per_page: 12))
  end

  # GET /purchase_events/1 or /purchase_events/1.json
  def show
    @purchase_event.purchase_event_products.build
  end

  # GET /purchase_events/new
  def new
    @purchase_event = PurchaseEvent.new
  end

  # GET /purchase_events/1/edit
  def edit
    # @purchase_event.purchase_event_products.build if @purchase_event.purchase_event_products.empty?
  end

  # POST /purchase_events or /purchase_events.json
  def create
    @purchase_event = PurchaseEvent.new purchase_event_params.except(:purchase_event_products_attributes)

    respond_to do |format|
      if @purchase_event.save
        create_purchase_items

        format.html do
          redirect_to purchase_event_url(@purchase_event), notice: 'Purchase event was successfully created.'
        end
        format.json { render :show, status: :created, location: @purchase_event }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @purchase_event.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /purchase_events/1 or /purchase_events/1.json
  def update
    respond_to do |format|
      if @purchase_event.update(purchase_event_params)
        format.html do
          redirect_to purchase_event_url(@purchase_event), notice: 'Purchase event was successfully updated.'
        end
        format.json { render :show, status: :ok, location: @purchase_event }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @purchase_event.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /purchase_events/1 or /purchase_events/1.json
  def destroy
    @purchase_event.destroy!

    respond_to do |format|
      format.html { redirect_to purchase_events_url, notice: 'Purchase event was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  def create_purchase_items
    purchase_event_params[:purchase_event_products_attributes].values.each do |attributes|
      next unless attributes[:_destroy].to_i.zero?

      purchase_event_product = PurchaseEventProduct.new attributes.except(:_destroy).merge(purchase_event_id: @purchase_event.id)

      if purchase_event_product.save
        Rails.logger.debug { "Successfully saved #{purchase_event_product.inspect}" }
      else
        Rails.logger.debug { "Failed to save #{purchase_event_product.inspect}" }
      end
      Rails.logger.debug attributes.except(:_destroy).merge(purchase_event_id: @purchase_event.id).inspect
      Rails.logger.debug '-' * 75
    end
  end

  def set_available_buyers
    @available_buyers = Persona.where.not(alias_for: nil).order(:nickname)
  end

  def set_available_products
    @available_products = Product.order(:name)
  end

  def set_available_sellers
    @available_sellers = Persona.order(:nickname)
  end

  def set_purchase_event
    @purchase_event = PurchaseEvent.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def purchase_event_params
    params.require(:purchase_event).permit(
      :buyer_id,
      :seller_id,
      :purchased_at,
      purchase_event_products_attributes: %i[
        _destroy
        currency_id
        expiry_date
        id
        original_price
        paid_price
        product_id
        production_date
        quantity
        unit_id
      ]
    )
  end
end
