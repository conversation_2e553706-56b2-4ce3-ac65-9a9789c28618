class RefuelStationsController < ApplicationController
  before_action :set_refuel_station, only: %i[show edit update destroy]

  # GET /refuel_stations or /refuel_stations.json
  def index
    @refuel_stations = RefuelStation.all
      .then(&apply_pagination(per_page: 12))
  end

  # GET /refuel_stations/1 or /refuel_stations/1.json
  def show; end

  # GET /refuel_stations/new
  def new
    @refuel_station = RefuelStation.new
  end

  # GET /refuel_stations/1/edit
  def edit; end

  # POST /refuel_stations or /refuel_stations.json
  def create
    @refuel_station = RefuelStation.new(refuel_station_params)

    respond_to do |format|
      if @refuel_station.save
        format.html do
          redirect_to refuel_station_url(@refuel_station), notice: 'Refuel station was successfully created.'
        end
        format.json { render :show, status: :created, location: @refuel_station }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @refuel_station.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /refuel_stations/1 or /refuel_stations/1.json
  def update
    respond_to do |format|
      if @refuel_station.update(refuel_station_params)
        format.html do
          redirect_to refuel_station_url(@refuel_station), notice: 'Refuel station was successfully updated.'
        end
        format.json { render :show, status: :ok, location: @refuel_station }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @refuel_station.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /refuel_stations/1 or /refuel_stations/1.json
  def destroy
    @refuel_station.destroy!

    respond_to do |format|
      format.html { redirect_to refuel_stations_url, notice: 'Refuel station was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_refuel_station
    @refuel_station = RefuelStation.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def refuel_station_params
    params.require(:refuel_station).permit(:address, :brand_id, :city_id, :latitude, :longitude, :nickname)
  end
end
