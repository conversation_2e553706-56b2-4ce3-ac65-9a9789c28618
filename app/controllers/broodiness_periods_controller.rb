class BroodinessPeriodsController < ApplicationController
  before_action :set_broodiness_period, only: %i[show edit update destroy]
  before_action :set_available_chickens, only: %i[new edit create update]

  # GET /broodiness_periods or /broodiness_periods.json
  def index
    @broodiness_periods = authorize BroodinessPeriod.order(start_day: :desc)
    @broodiness_periods = @broodiness_periods.where(chicken_id: params[:chicken_id]) if params[:chicken_id].present?
    @broodiness_periods = @broodiness_periods.then(&apply_pagination(per_page: 12))
  end

  # GET /broodiness_periods/1 or /broodiness_periods/1.json
  def show
    authorize @broodiness_period
  end

  # GET /broodiness_periods/new
  def new
    @broodiness_period = authorize BroodinessPeriod.new
  end

  # GET /broodiness_periods/1/edit
  def edit; end

  # POST /broodiness_periods or /broodiness_periods.json
  def create
    @broodiness_period = BroodinessPeriod.new(broodiness_period_params)

    respond_to do |format|
      if @broodiness_period.save
        format.html do
          redirect_to broodiness_period_url(@broodiness_period), notice: 'Broodiness period was successfully created.'
        end
        format.json { render :show, status: :created, location: @broodiness_period }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @broodiness_period.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /broodiness_periods/1 or /broodiness_periods/1.json
  def update
    respond_to do |format|
      if @broodiness_period.update(broodiness_period_params)
        format.html do
          redirect_to broodiness_period_url(@broodiness_period), notice: 'Broodiness period was successfully updated.'
        end
        format.json { render :show, status: :ok, location: @broodiness_period }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @broodiness_period.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /broodiness_periods/1 or /broodiness_periods/1.json
  def destroy
    @broodiness_period.destroy!

    respond_to do |format|
      format.html { redirect_to broodiness_periods_url, notice: 'Broodiness period was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_broodiness_period
    @broodiness_period = BroodinessPeriod.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def broodiness_period_params
    params.fetch(:broodiness_period, {})
      .permit(:chicken_id, :start_day, :end_day)
  end

  def set_available_chickens
    date = broodiness_period_params[:start_day].presence || @broodiness_period&.start_day || Time.zone.today
    date = DateTime.parse(date) if date.is_a? String

    @available_chickens = authorize Chicken.female.order(:name).alive(date)
  end
end
