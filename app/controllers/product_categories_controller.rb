class ProductCategoriesController < ApplicationController
  before_action :set_product_category, only: %i[show edit update destroy]

  # GET /product_categories or /product_categories.json
  def index
    @product_categories_tree = ProductCategory.build_tree
  end

  # GET /product_categories/1 or /product_categories/1.json
  def show
    authorize @product_category
  end

  # GET /product_categories/new
  def new
    @product_category = authorize ProductCategory.new
  end

  # GET /product_categories/1/edit
  def edit
    authorize @product_category
  end

  # POST /product_categories or /product_categories.json
  def create
    @product_category = authorize ProductCategory.new(product_category_params)

    respond_to do |format|
      if @product_category.save
        format.html do
          redirect_to product_category_url(@product_category), notice: 'Product category was successfully created.'
        end
        format.json { render :show, status: :created, location: @product_category }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @product_category.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /product_categories/1 or /product_categories/1.json
  def update
    respond_to do |format|
      if (authorize @product_category).update(product_category_params)
        format.html do
          redirect_to product_category_url(@product_category), notice: 'Product category was successfully updated.'
        end
        format.json { render :show, status: :ok, location: @product_category }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @product_category.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /product_categories/1 or /product_categories/1.json
  def destroy
    (authorize @product_category).destroy!

    respond_to do |format|
      format.html { redirect_to product_categories_url, notice: 'Product category was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_product_category
    @product_category = ProductCategory.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def product_category_params
    params.require(:product_category).permit(:name, :parent_id)
  end
end
