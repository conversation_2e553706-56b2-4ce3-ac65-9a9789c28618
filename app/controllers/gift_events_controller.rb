class GiftEventsController < ApplicationController
  before_action :set_gift_event, only: %i[show edit update destroy]
  before_action :set_available_eggs, only: %i[new edit]
  before_action :set_available_personas, only: %i[new edit]

  # GET /gift_events or /gift_events.json
  def index
    @gift_events = GiftEvent.order(date: :desc)
      .then(&apply_pagination(per_page: 12))
  end

  # GET /gift_events/1 or /gift_events/1.json
  def show
    set_chickens_contribution_to_event(authorize(@gift_event))
  end

  # GET /gift_events/new
  def new
    @gift_event = GiftEvent.new
  end

  # GET /gift_events/1/edit
  def edit; end

  # POST /gift_events or /gift_events.json
  def create
    @gift_event = GiftEvent.new(gift_event_params)

    respond_to do |format|
      if @gift_event.save
        format.html { redirect_to gift_event_url(@gift_event), notice: 'Gift event was successfully created.' }
        format.json { render :show, status: :created, location: @gift_event }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.json { render json: @gift_event.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /gift_events/1 or /gift_events/1.json
  def update
    respond_to do |format|
      if @gift_event.update(gift_event_params)
        format.html { redirect_to gift_event_url(@gift_event), notice: 'Gift event was successfully updated.' }
        format.json { render :show, status: :ok, location: @gift_event }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.json { render json: @gift_event.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /gift_events/1 or /gift_events/1.json
  def destroy
    @gift_event.destroy!

    respond_to do |format|
      format.html { redirect_to gift_events_url, notice: 'Gift event was successfully destroyed.' }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_available_personas
    @available_personas = Persona.order(:nickname)
  end

  def set_available_eggs
    date = begin
      DateTime.parse gift_event_params[:date]
    rescue StandardError
      @gift_event&.date || 1.second.ago
    end

    not_evented_eggs = Egg
      .where(food_event_id: nil, gift_event_id: nil, sell_event_id: nil)
      .where(laid_at: (date - 3.months).beginning_of_day..date.end_of_day)
      .order(id: :desc)

    already_included_eggs = Egg.where(gift_event_id: @gift_event&.id || -1)

    @available_eggs = not_evented_eggs.or(already_included_eggs)
  end

  def set_gift_event
    @gift_event = GiftEvent.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def gift_event_params
    params.require(:gift_event).permit(
      :date,
      :persona_id,
      egg_ids: []
    )
  end

  def set_chickens_contribution_to_event(event)
    chicken_contributions = Hash.new do |hash, key|
      hash[key] = { chicken: nil, contribution: 0.0, contribution_rate: 0.0, normalized_contribution_rate: 0.0 }
    end

    total_eggs = event.eggs.count
    max_contribution_rate = 0.0

    event.eggs.each do |egg|
      # Get all chickens associated with the egg
      chickens = egg.chickens
      next if chickens.empty?

      # Calculate the contribution (1/n) for this particular egg
      contribution = 1.0 / chickens.count

      # Add or update each chicken's contribution probability
      chickens.find_each do |chicken|
        chicken_contributions[chicken.id][:chicken] = chicken
        chicken_contributions[chicken.id][:contribution] += contribution
        chicken_contributions[chicken.id][:contribution_rate] =
          chicken_contributions[chicken.id][:contribution] / total_eggs
        if chicken_contributions[chicken.id][:contribution_rate] > max_contribution_rate
          max_contribution_rate = chicken_contributions[chicken.id][:contribution_rate]
        end
      end
    end

    if max_contribution_rate.positive?
      chicken_contributions.each do |_, contribution_data|
        contribution_data[:normalized_contribution_rate] =
          contribution_data[:contribution_rate] / max_contribution_rate
      end
    end

    @chicken_contributions = chicken_contributions
  end
end
