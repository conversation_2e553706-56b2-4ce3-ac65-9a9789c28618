class ApplicationController < ActionController::Base
  include Pundit::Authorization

  rescue_from Pundit::NotAuthorizedError, with: :user_not_authorized
  rescue_from ActiveRecord::RecordNotFound, with: :record_not_found

  protected

  def apply_pagination(per_page: 24)
    ->(collection) { collection.paginate(page: params[:page] || 1, per_page: params[:per_page] || per_page.to_i) }
  end

  def param_missing?(param) = params[param].blank? || params[param].empty?

  private

  def record_not_found
    resource_name = controller_name
    flash[:alert] = 'Not found.'
    redirect_to url_for(controller: resource_name, action: :index)
  end

  def user_not_authorized
    flash[:alert] = I18n.t('flash.not_authorized')
    redirect_to(request.referer || root_path)
  end
end
