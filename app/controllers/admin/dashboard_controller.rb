class Admin::DashboardController < ApplicationController
  before_action :authenticate_user!
  before_action :ensure_admin!

  def index
    @eggs_count = Egg.count
    @sold_price = SellEvent.pluck(:price).compact.sum
    @meals = FoodEvent.count
    @weight_records = WeightRecord.count
    @users = User.order(:email)
  end

  private

  def ensure_admin!
    redirect_to root_path unless current_user&.admin?
  end
end
