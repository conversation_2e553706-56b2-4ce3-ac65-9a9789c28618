services:
  db:
    image: postgres:15.1
    container_name: bergamon_family_db
    environment:
      - POSTGRES_DB=app_development
      - POSTGRES_PASSWORD=password
    restart: unless-stopped
    volumes:
      - ./volumes/db:/var/lib/postgresql/data

  test_db:
    image: postgres:15.1
    container_name: bergamon_family_test_db
    environment:
      - POSTGRES_DB=app_test
      - POSTGRES_PASSWORD=password
    restart: unless-stopped
    volumes:
      - ./volumes/test_db:/var/lib/postgresql/data

  web:
    build: .
    container_name: bergamon_family_rails
    environment:
      - POSTGRES_USERNAME=postgres
      - POSTGRES_PASSWORD=password
    # command: rails server -p 3000 -b 0.0.0.0
    # command: bundle || tail -F anything
    restart: "no"
    # stdin_open: true
    # tty: true
    command: tail -f /dev/null
    volumes:
      - .:/app
      - ./volumes/bundle:/usr/local/bundle
    working_dir: /app
    # Do the port mapping in your docker-compose.override.yml file
    # ports:
    #   - "192.168.0.159:3004:3000"
    depends_on:
      - db

volumes:
  db:
  web:
